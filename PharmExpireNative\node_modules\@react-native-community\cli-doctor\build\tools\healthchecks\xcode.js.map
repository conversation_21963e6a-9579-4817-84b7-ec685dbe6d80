{"version": 3, "names": ["label", "description", "getDiagnostics", "IDEs", "version", "Xcode", "split", "needsToBeFixed", "doesSoftwareNeedToBeFixed", "versionRange", "versionRanges", "XCODE", "runAutomaticFix", "loader", "logManualInstallation", "fail", "healthcheck", "url"], "sources": ["../../../src/tools/healthchecks/xcode.ts"], "sourcesContent": ["import versionRanges from '../versionRanges';\nimport {doesSoftwareNeedToBeFixed} from '../checkInstallation';\nimport {HealthCheckInterface} from '../../types';\n\nexport default {\n  label: 'Xcode',\n  description: 'Required for building and installing your app on iOS',\n  getDiagnostics: async ({IDEs}) => {\n    const version = IDEs.Xcode.version.split('/')[0];\n\n    return {\n      needsToBeFixed: doesSoftwareNeedToBeFixed({\n        version,\n        versionRange: versionRanges.XCODE,\n      }),\n      version,\n      versionRange: versionRanges.XCODE,\n    };\n  },\n  runAutomaticFix: async ({loader, logManualInstallation}) => {\n    loader.fail();\n\n    logManualInstallation({\n      healthcheck: 'Xcode',\n      url: 'https://developer.apple.com/xcode/',\n    });\n  },\n} as HealthCheckInterface;\n"], "mappings": ";;;;;;AAAA;AACA;AAA+D;AAAA,eAGhD;EACbA,KAAK,EAAE,OAAO;EACdC,WAAW,EAAE,sDAAsD;EACnEC,cAAc,EAAE,OAAO;IAACC;EAAI,CAAC,KAAK;IAChC,MAAMC,OAAO,GAAGD,IAAI,CAACE,KAAK,CAACD,OAAO,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEhD,OAAO;MACLC,cAAc,EAAE,IAAAC,4CAAyB,EAAC;QACxCJ,OAAO;QACPK,YAAY,EAAEC,sBAAa,CAACC;MAC9B,CAAC,CAAC;MACFP,OAAO;MACPK,YAAY,EAAEC,sBAAa,CAACC;IAC9B,CAAC;EACH,CAAC;EACDC,eAAe,EAAE,OAAO;IAACC,MAAM;IAAEC;EAAqB,CAAC,KAAK;IAC1DD,MAAM,CAACE,IAAI,EAAE;IAEbD,qBAAqB,CAAC;MACpBE,WAAW,EAAE,OAAO;MACpBC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ;AACF,CAAC;AAAA"}