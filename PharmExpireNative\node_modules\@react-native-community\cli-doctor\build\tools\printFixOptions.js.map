{"version": 3, "names": ["KEYS", "FIX_ALL_ISSUES", "FIX_ERRORS", "FIX_WARNINGS", "EXIT", "printOption", "option", "logger", "log", "printOptions", "chalk", "bold", "dim", "onKeyPress", "process", "stdin", "setRawMode", "resume", "setEncoding", "on"], "sources": ["../../src/tools/printFixOptions.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport {logger} from '@react-native-community/cli-tools';\n\nconst KEYS = {\n  FIX_ALL_ISSUES: 'f',\n  FIX_ERRORS: 'e',\n  FIX_WARNINGS: 'w',\n  EXIT: '\\r',\n};\n\nconst printOption = (option: string) => logger.log(` \\u203A ${option}`);\nconst printOptions = () => {\n  logger.log();\n  logger.log(chalk.bold('Usage'));\n  printOption(\n    `${chalk.dim('Press')} ${KEYS.FIX_ALL_ISSUES} ${chalk.dim(\n      'to try to fix issues.',\n    )}`,\n  );\n  printOption(\n    `${chalk.dim('Press')} ${KEYS.FIX_ERRORS} ${chalk.dim(\n      'to try to fix errors.',\n    )}`,\n  );\n  printOption(\n    `${chalk.dim('Press')} ${KEYS.FIX_WARNINGS} ${chalk.dim(\n      'to try to fix warnings.',\n    )}`,\n  );\n  printOption(`${chalk.dim('Press')} Enter ${chalk.dim('to exit.')}`);\n};\n\nexport {KEYS};\nexport default ({onKeyPress}: {onKeyPress: (...args: any[]) => void}) => {\n  printOptions();\n\n  if (process.stdin.setRawMode) {\n    process.stdin.setRawMode(true);\n  }\n  process.stdin.resume();\n  process.stdin.setEncoding('utf8');\n  process.stdin.on('data', onKeyPress);\n};\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAyD;AAEzD,MAAMA,IAAI,GAAG;EACXC,cAAc,EAAE,GAAG;EACnBC,UAAU,EAAE,GAAG;EACfC,YAAY,EAAE,GAAG;EACjBC,IAAI,EAAE;AACR,CAAC;AAAC;AAEF,MAAMC,WAAW,GAAIC,MAAc,IAAKC,kBAAM,CAACC,GAAG,CAAE,WAAUF,MAAO,EAAC,CAAC;AACvE,MAAMG,YAAY,GAAG,MAAM;EACzBF,kBAAM,CAACC,GAAG,EAAE;EACZD,kBAAM,CAACC,GAAG,CAACE,gBAAK,CAACC,IAAI,CAAC,OAAO,CAAC,CAAC;EAC/BN,WAAW,CACR,GAAEK,gBAAK,CAACE,GAAG,CAAC,OAAO,CAAE,IAAGZ,IAAI,CAACC,cAAe,IAAGS,gBAAK,CAACE,GAAG,CACvD,uBAAuB,CACvB,EAAC,CACJ;EACDP,WAAW,CACR,GAAEK,gBAAK,CAACE,GAAG,CAAC,OAAO,CAAE,IAAGZ,IAAI,CAACE,UAAW,IAAGQ,gBAAK,CAACE,GAAG,CACnD,uBAAuB,CACvB,EAAC,CACJ;EACDP,WAAW,CACR,GAAEK,gBAAK,CAACE,GAAG,CAAC,OAAO,CAAE,IAAGZ,IAAI,CAACG,YAAa,IAAGO,gBAAK,CAACE,GAAG,CACrD,yBAAyB,CACzB,EAAC,CACJ;EACDP,WAAW,CAAE,GAAEK,gBAAK,CAACE,GAAG,CAAC,OAAO,CAAE,UAASF,gBAAK,CAACE,GAAG,CAAC,UAAU,CAAE,EAAC,CAAC;AACrE,CAAC;AAAC,eAGa,CAAC;EAACC;AAAkD,CAAC,KAAK;EACvEJ,YAAY,EAAE;EAEd,IAAIK,OAAO,CAACC,KAAK,CAACC,UAAU,EAAE;IAC5BF,OAAO,CAACC,KAAK,CAACC,UAAU,CAAC,IAAI,CAAC;EAChC;EACAF,OAAO,CAACC,KAAK,CAACE,MAAM,EAAE;EACtBH,OAAO,CAACC,KAAK,CAACG,WAAW,CAAC,MAAM,CAAC;EACjCJ,OAAO,CAACC,KAAK,CAACI,EAAE,CAAC,MAAM,EAAEN,UAAU,CAAC;AACtC,CAAC;AAAA"}