{"version": 3, "names": ["runSudo", "command", "Promise", "resolve", "reject", "sudo", "exec", "name", "error"], "sources": ["../src/runSudo.ts"], "sourcesContent": ["import sudo from '@vscode/sudo-prompt';\n\nexport default function runSudo(command: string): Promise<void> {\n  return new Promise((resolve, reject) => {\n    sudo.exec(command, {name: 'React Native CLI'}, (error) => {\n      if (error) {\n        reject(error);\n      }\n\n      resolve();\n    });\n  });\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAuC;AAExB,SAASA,OAAO,CAACC,OAAe,EAAiB;EAC9D,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtCC,qBAAI,CAACC,IAAI,CAACL,OAAO,EAAE;MAACM,IAAI,EAAE;IAAkB,CAAC,EAAGC,KAAK,IAAK;MACxD,IAAIA,KAAK,EAAE;QACTJ,MAAM,CAACI,KAAK,CAAC;MACf;MAEAL,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ"}