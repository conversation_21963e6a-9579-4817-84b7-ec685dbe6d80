{"version": 3, "names": ["StreamZip", "require", "unzip", "source", "destination", "Promise", "resolve", "reject", "zip", "file", "storeEntries", "mkdirSync", "recursive", "on", "extract", "err", "close", "undefined"], "sources": ["../../src/tools/unzip.ts"], "sourcesContent": ["import {mkdirSync} from 'fs';\nconst StreamZip = require('node-stream-zip');\n\nconst unzip = async (source: string, destination: string) => {\n  return new Promise((resolve, reject) => {\n    const zip = new StreamZip({\n      file: source,\n      storeEntries: true,\n    });\n\n    mkdirSync(destination, {recursive: true});\n\n    zip.on('ready', () => {\n      zip.extract(null, destination, (err: Error | null) => {\n        zip.close();\n\n        if (err) {\n          return reject(err);\n        }\n\n        resolve(undefined);\n      });\n    });\n  });\n};\n\nexport {unzip};\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA,MAAMA,SAAS,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAE5C,MAAMC,KAAK,GAAG,OAAOC,MAAc,EAAEC,WAAmB,KAAK;EAC3D,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMC,GAAG,GAAG,IAAIR,SAAS,CAAC;MACxBS,IAAI,EAAEN,MAAM;MACZO,YAAY,EAAE;IAChB,CAAC,CAAC;IAEF,IAAAC,eAAS,EAACP,WAAW,EAAE;MAACQ,SAAS,EAAE;IAAI,CAAC,CAAC;IAEzCJ,GAAG,CAACK,EAAE,CAAC,OAAO,EAAE,MAAM;MACpBL,GAAG,CAACM,OAAO,CAAC,IAAI,EAAEV,WAAW,EAAGW,GAAiB,IAAK;QACpDP,GAAG,CAACQ,KAAK,EAAE;QAEX,IAAID,GAAG,EAAE;UACP,OAAOR,MAAM,CAACQ,GAAG,CAAC;QACpB;QAEAT,OAAO,CAACW,SAAS,CAAC;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AAAC"}