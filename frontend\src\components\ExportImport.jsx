import React, { useState } from 'react';
import { Download, Upload, FileText, AlertCircle } from 'lucide-react';
import { itemsAPI } from '../services/api';

const ExportImport = ({ onImportComplete }) => {
  const [importing, setImporting] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [importError, setImportError] = useState(null);

  const exportData = async () => {
    try {
      setExporting(true);
      const response = await itemsAPI.getAll();
      const data = response.data;

      // Create CSV content
      const headers = ['Item Name', 'Category', 'Expiration Date', 'Quantity', 'Location', 'Notes'];
      const csvContent = [
        headers.join(','),
        ...data.map(item => [
          `"${item.item_name}"`,
          `"${item.category}"`,
          item.expiration_date,
          item.quantity || 1,
          `"${item.location || ''}"`,
          `"${item.notes || ''}"`
        ].join(','))
      ].join('\n');

      // Download file
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `pharmacy-inventory-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
      alert('Failed to export data');
    } finally {
      setExporting(false);
    }
  };

  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    setImporting(true);
    setImportError(null);

    try {
      const text = await file.text();
      const lines = text.split('\n').filter(line => line.trim());
      
      if (lines.length < 2) {
        throw new Error('File must contain at least a header and one data row');
      }

      const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());
      const requiredHeaders = ['Item Name', 'Category', 'Expiration Date'];
      
      const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));
      if (missingHeaders.length > 0) {
        throw new Error(`Missing required columns: ${missingHeaders.join(', ')}`);
      }

      const validCategories = ['Food', 'Medicine', 'Mom & Baby', 'Cosmetic', 'Wellness'];
      let successCount = 0;
      let errorCount = 0;

      for (let i = 1; i < lines.length; i++) {
        try {
          const values = lines[i].split(',').map(v => v.replace(/"/g, '').trim());
          const rowData = {};
          
          headers.forEach((header, index) => {
            rowData[header] = values[index] || '';
          });

          // Validate required fields
          if (!rowData['Item Name'] || !rowData['Category'] || !rowData['Expiration Date']) {
            throw new Error(`Row ${i + 1}: Missing required fields`);
          }

          if (!validCategories.includes(rowData['Category'])) {
            throw new Error(`Row ${i + 1}: Invalid category "${rowData['Category']}"`);
          }

          // Create item
          await itemsAPI.create({
            item_name: rowData['Item Name'],
            category: rowData['Category'],
            expiration_date: rowData['Expiration Date'],
            quantity: parseInt(rowData['Quantity']) || 1,
            location: rowData['Location'] || '',
            notes: rowData['Notes'] || ''
          });

          successCount++;
        } catch (error) {
          console.error(`Error importing row ${i + 1}:`, error);
          errorCount++;
        }
      }

      if (successCount > 0) {
        alert(`Import completed! ${successCount} items imported successfully${errorCount > 0 ? `, ${errorCount} items failed` : ''}.`);
        onImportComplete?.();
      } else {
        throw new Error('No items were imported successfully');
      }

    } catch (error) {
      setImportError(error.message);
    } finally {
      setImporting(false);
      event.target.value = ''; // Reset file input
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center gap-2 mb-4">
        <FileText className="h-5 w-5 text-gray-600" />
        <h2 className="text-xl font-semibold text-gray-900">Export / Import Data</h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Export Section */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-900">Export Data</h3>
          <p className="text-sm text-gray-600">
            Download your inventory data as a CSV file for backup or external use.
          </p>
          <button
            onClick={exportData}
            disabled={exporting}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors"
          >
            {exporting ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Download className="h-4 w-4" />
            )}
            {exporting ? 'Exporting...' : 'Export to CSV'}
          </button>
        </div>

        {/* Import Section */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-900">Import Data</h3>
          <p className="text-sm text-gray-600">
            Upload a CSV file to import items. Required columns: Item Name, Category, Expiration Date.
          </p>
          
          <div className="relative">
            <input
              type="file"
              accept=".csv"
              onChange={handleFileUpload}
              disabled={importing}
              className="hidden"
              id="csv-upload"
            />
            <label
              htmlFor="csv-upload"
              className={`w-full border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-gray-400 transition-colors ${
                importing ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              <div className="flex flex-col items-center gap-2">
                {importing ? (
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                ) : (
                  <Upload className="h-6 w-6 text-gray-400" />
                )}
                <span className="text-sm text-gray-600">
                  {importing ? 'Importing...' : 'Click to upload CSV file'}
                </span>
              </div>
            </label>
          </div>

          {importError && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-red-800">{importError}</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* CSV Format Help */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium text-gray-900 mb-2">CSV Format Requirements</h4>
        <div className="text-sm text-gray-600 space-y-1">
          <p><strong>Required columns:</strong> Item Name, Category, Expiration Date</p>
          <p><strong>Optional columns:</strong> Quantity, Location, Notes</p>
          <p><strong>Valid categories:</strong> Food, Medicine, Mom & Baby, Cosmetic, Wellness</p>
          <p><strong>Date format:</strong> YYYY-MM-DD (e.g., 2024-12-31)</p>
        </div>
      </div>
    </div>
  );
};

export default ExportImport;
