{"version": 3, "names": ["listeners", "prompt", "question", "options", "pauseInteractions", "results", "prompts", "onCancel", "CLIError", "resumeInteractions", "logger", "debug", "listener", "pause", "addInteractionListener", "callback", "push"], "sources": ["../src/prompt.ts"], "sourcesContent": ["import prompts, {Options, PromptObject} from 'prompts';\nimport {CLIError} from './errors';\nimport logger from './logger';\n\ntype PromptOptions = {nonInteractiveHelp?: string} & Options;\ntype InteractionOptions = {pause: boolean; canEscape?: boolean};\ntype InteractionCallback = (options: InteractionOptions) => void;\n\n/** Interaction observers for detecting when keystroke tracking should pause/resume. */\nconst listeners: InteractionCallback[] = [];\n\nexport async function prompt<T extends string>(\n  question: PromptObject,\n  options: PromptOptions = {},\n): Promise<prompts.Answers<T>> {\n  pauseInteractions();\n  try {\n    const results = await prompts(question, {\n      onCancel() {\n        throw new CLIError('Prompt cancelled.');\n      },\n      ...options,\n    });\n\n    return results;\n  } finally {\n    resumeInteractions();\n  }\n}\n\nexport function pauseInteractions(\n  options: Omit<InteractionOptions, 'pause'> = {},\n) {\n  logger.debug('Interaction observers paused');\n  for (const listener of listeners) {\n    listener({pause: true, ...options});\n  }\n}\n\n/** Notify all listeners that keypress observations can start.. */\nexport function resumeInteractions(\n  options: Omit<InteractionOptions, 'pause'> = {},\n) {\n  logger.debug('Interaction observers resumed');\n  for (const listener of listeners) {\n    listener({pause: false, ...options});\n  }\n}\n\n/** Used to pause/resume interaction observers while prompting (made for TerminalUI). */\nexport function addInteractionListener(callback: InteractionCallback) {\n  listeners.push(callback);\n}\n"], "mappings": ";;;;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AAA8B;AAM9B;AACA,MAAMA,SAAgC,GAAG,EAAE;AAEpC,eAAeC,MAAM,CAC1BC,QAAsB,EACtBC,OAAsB,GAAG,CAAC,CAAC,EACE;EAC7BC,iBAAiB,EAAE;EACnB,IAAI;IACF,MAAMC,OAAO,GAAG,MAAM,IAAAC,kBAAO,EAACJ,QAAQ,EAAE;MACtCK,QAAQ,GAAG;QACT,MAAM,IAAIC,gBAAQ,CAAC,mBAAmB,CAAC;MACzC,CAAC;MACD,GAAGL;IACL,CAAC,CAAC;IAEF,OAAOE,OAAO;EAChB,CAAC,SAAS;IACRI,kBAAkB,EAAE;EACtB;AACF;AAEO,SAASL,iBAAiB,CAC/BD,OAA0C,GAAG,CAAC,CAAC,EAC/C;EACAO,eAAM,CAACC,KAAK,CAAC,8BAA8B,CAAC;EAC5C,KAAK,MAAMC,QAAQ,IAAIZ,SAAS,EAAE;IAChCY,QAAQ,CAAC;MAACC,KAAK,EAAE,IAAI;MAAE,GAAGV;IAAO,CAAC,CAAC;EACrC;AACF;;AAEA;AACO,SAASM,kBAAkB,CAChCN,OAA0C,GAAG,CAAC,CAAC,EAC/C;EACAO,eAAM,CAACC,KAAK,CAAC,+BAA+B,CAAC;EAC7C,KAAK,MAAMC,QAAQ,IAAIZ,SAAS,EAAE;IAChCY,QAAQ,CAAC;MAACC,KAAK,EAAE,KAAK;MAAE,GAAGV;IAAO,CAAC,CAAC;EACtC;AACF;;AAEA;AACO,SAASW,sBAAsB,CAACC,QAA6B,EAAE;EACpEf,SAAS,CAACgB,IAAI,CAACD,QAAQ,CAAC;AAC1B"}