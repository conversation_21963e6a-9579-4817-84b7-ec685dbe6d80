import db from '../database/db.js';

export class Item {
  static getAll() {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT * FROM items 
        ORDER BY expiration_date ASC
      `;
      
      db.all(sql, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  static getById(id) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM items WHERE id = ?';
      
      db.get(sql, [id], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  static getExpiringSoon(days = 7) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT * FROM items 
        WHERE DATE(expiration_date) <= DATE('now', '+${days} days')
        AND DATE(expiration_date) >= DATE('now')
        ORDER BY expiration_date ASC
      `;
      
      db.all(sql, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  static getExpired() {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT * FROM items 
        WHERE DATE(expiration_date) < DATE('now')
        ORDER BY expiration_date DESC
      `;
      
      db.all(sql, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  static getByCategory(category) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT * FROM items 
        WHERE category = ?
        ORDER BY expiration_date ASC
      `;
      
      db.all(sql, [category], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  static create(itemData) {
    return new Promise((resolve, reject) => {
      const { item_name, category, expiration_date, notes, quantity, location } = itemData;
      
      const sql = `
        INSERT INTO items (item_name, category, expiration_date, notes, quantity, location)
        VALUES (?, ?, ?, ?, ?, ?)
      `;
      
      db.run(sql, [item_name, category, expiration_date, notes, quantity, location], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, ...itemData });
        }
      });
    });
  }

  static update(id, itemData) {
    return new Promise((resolve, reject) => {
      const { item_name, category, expiration_date, notes, quantity, location } = itemData;
      
      const sql = `
        UPDATE items 
        SET item_name = ?, category = ?, expiration_date = ?, notes = ?, 
            quantity = ?, location = ?, updated_date = CURRENT_TIMESTAMP
        WHERE id = ?
      `;
      
      db.run(sql, [item_name, category, expiration_date, notes, quantity, location, id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id, ...itemData });
        }
      });
    });
  }

  static delete(id) {
    return new Promise((resolve, reject) => {
      const sql = 'DELETE FROM items WHERE id = ?';
      
      db.run(sql, [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ deletedId: id, changes: this.changes });
        }
      });
    });
  }

  static getStats() {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT 
          COUNT(*) as total_items,
          COUNT(CASE WHEN DATE(expiration_date) < DATE('now') THEN 1 END) as expired_items,
          COUNT(CASE WHEN DATE(expiration_date) <= DATE('now', '+7 days') AND DATE(expiration_date) >= DATE('now') THEN 1 END) as expiring_soon,
          COUNT(CASE WHEN category = 'Food' THEN 1 END) as food_items,
          COUNT(CASE WHEN category = 'Medicine' THEN 1 END) as medicine_items,
          COUNT(CASE WHEN category = 'Mom & Baby' THEN 1 END) as mom_baby_items,
          COUNT(CASE WHEN category = 'Cosmetic' THEN 1 END) as cosmetic_items,
          COUNT(CASE WHEN category = 'Wellness' THEN 1 END) as wellness_items
        FROM items
      `;
      
      db.get(sql, [], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }
}
