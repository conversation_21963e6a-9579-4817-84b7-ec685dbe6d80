ninja: Entering directory `D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-screens\android\.cxx\Debug\4z6u6o52\arm64-v8a'
[1/3] Building CXX object CMakeFiles/rnscreens.dir/9858fa4c5fe43baa2165615811ad4891/react-native-screens/cpp/RNScreensTurboModule.cpp.o
[2/3] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o
[3/3] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\4z6u6o52\obj\arm64-v8a\librnscreens.so
