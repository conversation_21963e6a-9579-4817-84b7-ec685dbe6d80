import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { calculateStats, getExpiredItems, getExpiringSoonItems, getAllItems } from '../data/mockData';

const { width } = Dimensions.get('window');

const ReportsScreen = ({ navigation }) => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('all'); // all, week, month

  useEffect(() => {
    fetchReports();
  }, [selectedPeriod]);

  const fetchReports = () => {
    try {
      setLoading(true);

      // الحصول على الإحصائيات الأساسية
      const statsData = calculateStats();

      if (!statsData) {
        setStats(null);
        setLoading(false);
        return;
      }

      // حساب إحصائيات إضافية بشكل مبسط
      const categoryBreakdown = [];
      if (statsData.categoryStats) {
        Object.entries(statsData.categoryStats).forEach(([category, count]) => {
          if (count > 0) {
            categoryBreakdown.push({
              category,
              count,
              percentage: statsData.totalItems > 0 ? ((count / statsData.totalItems) * 100).toFixed(1) : 0
            });
          }
        });
      }

      // اتجاهات بسيطة
      const trends = {
        totalGrowth: '+12%',
        expiredReduction: '-8%',
        newItemsThisWeek: 0
      };

      setStats({
        ...statsData,
        categoryBreakdown,
        trends
      });
    } catch (error) {
      console.error('خطأ في جلب التقارير:', error);
      setStats(null);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchReports();
    setRefreshing(false);
  };

  const ReportCard = ({ title, value, subtitle, icon, color, onPress }) => (
    <TouchableOpacity style={[styles.reportCard, { borderLeftColor: color }]} onPress={onPress}>
      <View style={styles.reportCardContent}>
        <View style={styles.reportCardLeft}>
          <Text style={styles.reportCardTitle}>{title}</Text>
          <Text style={[styles.reportCardValue, { color }]}>{value}</Text>
          {subtitle && <Text style={styles.reportCardSubtitle}>{subtitle}</Text>}
        </View>
        <View style={[styles.reportCardIcon, { backgroundColor: color }]}>
          <Icon name={icon} size={24} color="#fff" />
        </View>
      </View>
    </TouchableOpacity>
  );

  const CategoryBreakdownCard = ({ category, count, percentage, color }) => (
    <View style={styles.categoryBreakdownCard}>
      <View style={styles.categoryBreakdownHeader}>
        <Text style={styles.categoryBreakdownName}>{getCategoryName(category)}</Text>
        <Text style={[styles.categoryBreakdownPercentage, { color }]}>{percentage}%</Text>
      </View>
      <View style={styles.progressBarContainer}>
        <View style={[styles.progressBar, { width: `${percentage}%`, backgroundColor: color }]} />
      </View>
      <Text style={styles.categoryBreakdownCount}>{count} قطعة</Text>
    </View>
  );

  const getCategoryName = (category) => {
    const names = {
      medicine: 'أدوية',
      'mom&baby': 'أم وطفل',
      personal_care: 'عناية شخصية',
      nutraceutical: 'مكملات غذائية',
      beauty: 'تجميل'
    };
    return names[category] || category;
  };

  const getCategoryColor = (category) => {
    const colors = {
      medicine: '#3b82f6',
      'mom&baby': '#ec4899',
      personal_care: '#f97316',
      nutraceutical: '#10b981',
      beauty: '#8b5cf6'
    };
    return colors[category] || '#6b7280';
  };

  const PeriodSelector = () => (
    <View style={styles.periodSelector}>
      {[
        { key: 'all', label: 'الكل' },
        { key: 'week', label: 'هذا الأسبوع' },
        { key: 'month', label: 'هذا الشهر' }
      ].map((period) => (
        <TouchableOpacity
          key={period.key}
          style={[
            styles.periodButton,
            selectedPeriod === period.key && styles.periodButtonActive
          ]}
          onPress={() => setSelectedPeriod(period.key)}
        >
          <Text style={[
            styles.periodButtonText,
            selectedPeriod === period.key && styles.periodButtonTextActive
          ]}>
            {period.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  if (loading && !stats) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>جاري تحميل التقارير...</Text>
      </View>
    );
  }

  const hasItems = stats && stats.totalProducts > 0;

  return (
    <ScrollView
      style={styles.container}
      refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
    >
      {/* العنوان الرئيسي */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>تقارير الصيدلية</Text>
        <Text style={styles.headerSubtitle}>تحليل شامل لمخزونك</Text>
      </View>

      {!hasItems ? (
        <View style={styles.emptyContainer}>
          <Icon name="assessment" size={64} color="#d1d5db" />
          <Text style={styles.emptyTitle}>لا توجد بيانات للتقارير</Text>
          <Text style={styles.emptySubtitle}>
            أضف منتجات إلى مخزونك لعرض التقارير والإحصائيات
          </Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => navigation.navigate('Add')}
          >
            <Icon name="add-circle" size={20} color="#fff" />
            <Text style={styles.addButtonText}>إضافة منتج</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <>
          {/* محدد الفترة الزمنية */}
          <PeriodSelector />

          {/* الإحصائيات الرئيسية */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>📊 الإحصائيات الرئيسية</Text>
            <ReportCard
              title="إجمالي المنتجات"
              value={stats.totalItems || 0}
              subtitle={`${stats.totalProducts || 0} نوع منتج`}
              icon="inventory"
              color="#3b82f6"
              onPress={() => navigation.navigate('Items')}
            />
            <ReportCard
              title="منتجات منتهية الصلاحية"
              value={stats.expiredItems || 0}
              subtitle={stats.totalItems > 0 ? `${(((stats.expiredItems || 0) / stats.totalItems) * 100).toFixed(1)}% من المجموع` : '0% من المجموع'}
              icon="warning"
              color="#dc2626"
              onPress={() => navigation.navigate('Items', { filter: 'expired' })}
            />
            <ReportCard
              title="تنتهي قريباً"
              value={stats.expiringSoonItems || 0}
              subtitle="خلال 7 أيام"
              icon="schedule"
              color="#d97706"
              onPress={() => navigation.navigate('Items', { filter: 'expiring' })}
            />
            <ReportCard
              title="منتجات آمنة"
              value={stats.safeItems || 0}
              subtitle={stats.totalItems > 0 ? `${(((stats.safeItems || 0) / stats.totalItems) * 100).toFixed(1)}% من المجموع` : '0% من المجموع'}
              icon="check-circle"
              color="#10b981"
              onPress={() => navigation.navigate('Items', { filter: 'safe' })}
            />
          </View>

          {/* تحليل الفئات */}
          {stats.categoryBreakdown && stats.categoryBreakdown.length > 0 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>🏷️ تحليل الفئات</Text>
              {stats.categoryBreakdown
                .filter(item => item.count > 0)
                .sort((a, b) => b.count - a.count)
                .map((item, index) => (
                  <CategoryBreakdownCard
                    key={index}
                    category={item.category}
                    count={item.count}
                    percentage={item.percentage}
                    color={getCategoryColor(item.category)}
                  />
                ))}
            </View>
          )}

          {/* الاتجاهات والتحليلات */}
          {stats.trends && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>📈 الاتجاهات والتحليلات</Text>
              <View style={styles.trendsContainer}>
                <View style={styles.trendCard}>
                  <Icon name="trending-up" size={24} color="#10b981" />
                  <Text style={styles.trendTitle}>نمو المخزون</Text>
                  <Text style={[styles.trendValue, { color: '#10b981' }]}>{stats.trends.totalGrowth || 'N/A'}</Text>
                </View>
                <View style={styles.trendCard}>
                  <Icon name="trending-down" size={24} color="#dc2626" />
                  <Text style={styles.trendTitle}>تحسن الانتهاء</Text>
                  <Text style={[styles.trendValue, { color: '#10b981' }]}>{stats.trends.expiredReduction || 'N/A'}</Text>
                </View>
                <View style={styles.trendCard}>
                  <Icon name="add-circle" size={24} color="#3b82f6" />
                  <Text style={styles.trendTitle}>منتجات جديدة</Text>
                  <Text style={[styles.trendValue, { color: '#3b82f6' }]}>{stats.trends.newItemsThisWeek || 0}</Text>
                </View>
              </View>
            </View>
          )}

          {/* توصيات */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>💡 توصيات</Text>
            <View style={styles.recommendationsContainer}>
              {(stats.expiredItems || 0) > 0 && (
                <View style={styles.recommendationCard}>
                  <Icon name="warning" size={20} color="#dc2626" />
                  <Text style={styles.recommendationText}>
                    لديك {stats.expiredItems} منتج منتهي الصلاحية. يُنصح بمراجعتها وإزالتها من المخزون.
                  </Text>
                </View>
              )}
              {(stats.expiringSoonItems || 0) > 0 && (
                <View style={styles.recommendationCard}>
                  <Icon name="schedule" size={20} color="#d97706" />
                  <Text style={styles.recommendationText}>
                    {stats.expiringSoonItems} منتج ستنتهي صلاحيته قريباً. ضع خطة لاستخدامها أو بيعها.
                  </Text>
                </View>
              )}
              {(stats.expiredItems || 0) === 0 && (stats.expiringSoonItems || 0) === 0 && (stats.totalItems || 0) > 0 && (
                <View style={styles.recommendationCard}>
                  <Icon name="check-circle" size={20} color="#10b981" />
                  <Text style={styles.recommendationText}>
                    ممتاز! جميع منتجاتك في حالة جيدة. استمر في المراقبة المنتظمة.
                  </Text>
                </View>
              )}
              {(stats.totalItems || 0) === 0 && (
                <View style={styles.recommendationCard}>
                  <Icon name="info" size={20} color="#3b82f6" />
                  <Text style={styles.recommendationText}>
                    ابدأ بإضافة منتجات إلى مخزونك للحصول على توصيات مخصصة.
                  </Text>
                </View>
              )}
            </View>
          </View>
        </>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
  },
  loadingText: {
    fontSize: 16,
    color: '#6b7280',
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 24,
  },
  addButton: {
    backgroundColor: '#10b981',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
  },
  addButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  periodSelector: {
    flexDirection: 'row',
    margin: 20,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 4,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  periodButtonActive: {
    backgroundColor: '#3b82f6',
  },
  periodButtonText: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
  },
  periodButtonTextActive: {
    color: '#fff',
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
    textAlign: 'right',
  },
  reportCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  reportCardContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  reportCardLeft: {
    flex: 1,
  },
  reportCardTitle: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 4,
    textAlign: 'right',
  },
  reportCardValue: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'right',
    marginBottom: 2,
  },
  reportCardSubtitle: {
    fontSize: 12,
    color: '#9ca3af',
    textAlign: 'right',
  },
  reportCardIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryBreakdownCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  categoryBreakdownHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryBreakdownName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  categoryBreakdownPercentage: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  progressBarContainer: {
    height: 6,
    backgroundColor: '#e5e7eb',
    borderRadius: 3,
    marginBottom: 8,
  },
  progressBar: {
    height: '100%',
    borderRadius: 3,
  },
  categoryBreakdownCount: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'right',
  },
  trendsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  trendCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    width: (width - 60) / 3,
    marginBottom: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  trendTitle: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 8,
    marginBottom: 4,
    textAlign: 'center',
  },
  trendValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  recommendationsContainer: {
    gap: 12,
  },
  recommendationCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'flex-start',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  recommendationText: {
    flex: 1,
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
    marginLeft: 12,
    textAlign: 'right',
  },
});

export default ReportsScreen;
