// تنسيق التاريخ للعرض
export const formatDate = (date) => {
  const d = new Date(date);
  return d.toLocaleDateString('ar-SA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// تنسيق التاريخ للإدخال
export const formatDateForInput = (date) => {
  const d = new Date(date);
  return d.toISOString().split('T')[0];
};

// التحقق من انتهاء الصلاحية
export const isExpired = (expirationDate) => {
  return new Date(expirationDate) < new Date();
};

// التحقق من انتهاء الصلاحية قريباً
export const isExpiringSoon = (expirationDate, days = 7) => {
  const expDate = new Date(expirationDate);
  const today = new Date();
  const futureDate = new Date(today.getTime() + (days * 24 * 60 * 60 * 1000));
  
  return expDate > today && expDate < futureDate;
};

// حساب الأيام المتبقية حتى انتهاء الصلاحية
export const getDaysUntilExpiration = (expirationDate) => {
  const expDate = new Date(expirationDate);
  const today = new Date();
  const diffTime = expDate - today;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

// الحصول على حالة انتهاء الصلاحية مع الألوان
export const getExpirationStatus = (expirationDate) => {
  const daysUntil = getDaysUntilExpiration(expirationDate);
  
  if (daysUntil < 0) {
    return {
      status: 'expired',
      message: `انتهت منذ ${Math.abs(daysUntil)} يوم`,
      color: '#dc2626',
      backgroundColor: '#fef2f2',
      textColor: '#991b1b'
    };
  } else if (daysUntil <= 7) {
    return {
      status: 'expiring-soon',
      message: daysUntil === 0 ? 'تنتهي اليوم' : `تنتهي خلال ${daysUntil} يوم`,
      color: '#d97706',
      backgroundColor: '#fffbeb',
      textColor: '#92400e'
    };
  } else {
    return {
      status: 'safe',
      message: `تنتهي خلال ${daysUntil} يوم`,
      color: '#059669',
      backgroundColor: '#ecfdf5',
      textColor: '#065f46'
    };
  }
};

// الحصول على أيقونة الفئة
export const getCategoryIcon = (category) => {
  const icons = {
    'Food': '🍎',
    'Medicine': '💊',
    'Mom & Baby': '👶',
    'Cosmetic': '💄',
    'Wellness': '🌿'
  };
  
  return icons[category] || '📦';
};

// الحصول على لون الفئة
export const getCategoryColor = (category) => {
  const colors = {
    'Food': '#f97316',
    'Medicine': '#3b82f6',
    'Mom & Baby': '#ec4899',
    'Cosmetic': '#8b5cf6',
    'Wellness': '#10b981'
  };
  
  return colors[category] || '#6b7280';
};
