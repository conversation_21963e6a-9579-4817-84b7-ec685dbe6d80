{"version": 3, "names": ["launchEditor", "fileName", "lineNumber", "_watchFolders", "launchEditorImpl", "process", "env", "REACT_EDITOR"], "sources": ["../src/launchEditor.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport launchEditorImpl from 'launch-editor';\n\nexport default function launchEditor(\n  fileName: string,\n  lineNumber: number,\n  _watchFolders?: ReadonlyArray<string>,\n): void {\n  launchEditorImpl(`${fileName}:${lineNumber}`, process.env.REACT_EDITOR);\n}\n"], "mappings": ";;;;;;AASA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA6C;AAT7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIe,SAASA,YAAY,CAClCC,QAAgB,EAChBC,UAAkB,EAClBC,aAAqC,EAC/B;EACN,IAAAC,uBAAgB,EAAE,GAAEH,QAAS,IAAGC,UAAW,EAAC,EAAEG,OAAO,CAACC,GAAG,CAACC,YAAY,CAAC;AACzE"}