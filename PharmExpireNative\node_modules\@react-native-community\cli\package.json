{"name": "@react-native-community/cli", "version": "19.1.0", "description": "React Native CLI", "license": "MIT", "main": "build/index.js", "publishConfig": {"access": "public"}, "bin": {"rnc-cli": "build/bin.js"}, "files": ["build", "!*.d.ts", "!*.map", "setup_env.sh"], "engineStrict": true, "engines": {"node": ">=18"}, "jest": {"testEnvironment": "node"}, "dependencies": {"@react-native-community/cli-clean": "19.1.0", "@react-native-community/cli-config": "19.1.0", "@react-native-community/cli-doctor": "19.1.0", "@react-native-community/cli-server-api": "19.1.0", "@react-native-community/cli-tools": "19.1.0", "@react-native-community/cli-types": "19.1.0", "chalk": "^4.1.2", "commander": "^9.4.1", "deepmerge": "^4.3.0", "execa": "^5.0.0", "find-up": "^5.0.0", "fs-extra": "^8.1.0", "graceful-fs": "^4.1.3", "prompts": "^2.4.2", "semver": "^7.5.2"}, "devDependencies": {"@types/fs-extra": "^8.1.0", "@types/graceful-fs": "^4.1.3", "@types/hapi__joi": "^17.1.6", "@types/prompts": "^2.4.4", "@types/semver": "^6.0.2", "slash": "^3.0.0", "snapshot-diff": "^0.7.0"}, "homepage": "https://github.com/react-native-community/cli/tree/main/packages/cli", "repository": {"type": "git", "url": "https://github.com/react-native-community/cli.git", "directory": "packages/cli"}, "gitHead": "8f3ecef6520713e24a67869afcde51f0c466e828"}