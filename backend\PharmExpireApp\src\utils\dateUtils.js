export const formatDate = (date) => {
  const d = new Date(date);
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

export const formatDateForInput = (date) => {
  const d = new Date(date);
  return d.toISOString().split('T')[0];
};

export const isExpired = (expirationDate) => {
  return new Date(expirationDate) < new Date();
};

export const isExpiringSoon = (expirationDate, days = 7) => {
  const expDate = new Date(expirationDate);
  const today = new Date();
  const futureDate = new Date(today.getTime() + (days * 24 * 60 * 60 * 1000));
  
  return expDate > today && expDate < futureDate;
};

export const getDaysUntilExpiration = (expirationDate) => {
  const expDate = new Date(expirationDate);
  const today = new Date();
  const diffTime = expDate - today;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

export const getExpirationStatus = (expirationDate) => {
  const daysUntil = getDaysUntilExpiration(expirationDate);
  
  if (daysUntil < 0) {
    return {
      status: 'expired',
      message: `Expired ${Math.abs(daysUntil)} day${Math.abs(daysUntil) !== 1 ? 's' : ''} ago`,
      color: '#dc2626',
      backgroundColor: '#fef2f2',
      textColor: '#991b1b'
    };
  } else if (daysUntil <= 7) {
    return {
      status: 'expiring-soon',
      message: daysUntil === 0 ? 'Expires today' : `Expires in ${daysUntil} day${daysUntil !== 1 ? 's' : ''}`,
      color: '#d97706',
      backgroundColor: '#fffbeb',
      textColor: '#92400e'
    };
  } else {
    return {
      status: 'safe',
      message: `Expires in ${daysUntil} day${daysUntil !== 1 ? 's' : ''}`,
      color: '#059669',
      backgroundColor: '#ecfdf5',
      textColor: '#065f46'
    };
  }
};

export const getCategoryIcon = (category) => {
  const icons = {
    'Food': '🍎',
    'Medicine': '💊',
    'Mom & Baby': '👶',
    'Cosmetic': '💄',
    'Wellness': '🌿'
  };
  
  return icons[category] || '📦';
};

export const getCategoryColor = (category) => {
  const colors = {
    'Food': '#f97316',
    'Medicine': '#3b82f6',
    'Mom & Baby': '#ec4899',
    'Cosmetic': '#8b5cf6',
    'Wellness': '#10b981'
  };
  
  return colors[category] || '#6b7280';
};
