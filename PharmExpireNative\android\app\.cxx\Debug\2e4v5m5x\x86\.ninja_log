# ninja log v5
1	41	0	D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/CMakeFiles/cmake.verify_globs	9c3adab04f9596aa
52	1011	7742461658086312	build.ninja	952bf45ed2f29eb4
0	19	0	clean	d4f0c3606ddd5f71
2	49	0	D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/CMakeFiles/cmake.verify_globs	9c3adab04f9596aa
472	5256	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	63160c126bacffe2
979	5317	7742461713038840	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	76fb50aec8367827
3	6833	7742461728040723	CMakeFiles/appmodules.dir/OnLoad.cpp.o	a3095f3dea8c9cb7
1184	7443	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	883cff9df3b0cbff
1667	8302	7742461742874031	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	fb29fc2ddbb505be
2145	8479	7742461744735924	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	f90ee140e555b6b5
1455	8499	7742461744901148	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	ddb7794e7526767c
1892	8629	7742461746200800	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	cf4e906df6b0c138
751	8708	7742461746793027	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	197689b3b486bd98
3014	8723	7742461747217884	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o	2457076fe32e4d94
254	8976	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	d0d71768108a47d6
2723	9100	0	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	9d7994fa1d168709
4413	9194	7742461751996643	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	6e8e2095231c83a8
2423	9476	7742461754729276	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/ComponentDescriptors.cpp.o	e400cdb5fcda0450
4075	10298	7742461762968503	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	cf5a6406c9ec8669
3296	11918	7742461779005093	CMakeFiles/appmodules.dir/b768dd175032fd29603a1b210f3f8932/PharmExpireNative/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	ac4a3a0ae031f246
11919	13725	7742461797191986	D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/build/intermediates/cxx/Debug/2e4v5m5x/obj/x86/libappmodules.so	296af68488800f8e
3	70	0	D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/CMakeFiles/cmake.verify_globs	9c3adab04f9596aa
317	4319	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	63160c126bacffe2
586	5112	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	883cff9df3b0cbff
920	5327	0	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	9d7994fa1d168709
20	6515	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	d0d71768108a47d6
6515	8785	7742467200698931	D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/build/intermediates/cxx/Debug/2e4v5m5x/obj/x86/libappmodules.so	296af68488800f8e
3	53	0	D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/CMakeFiles/cmake.verify_globs	9c3adab04f9596aa
1	26	0	clean	d4f0c3606ddd5f71
