{"modules": {"NativeSafeAreaContext": {"type": "NativeModule", "aliasMap": {}, "enumMap": {}, "spec": {"eventEmitters": [], "methods": [{"name": "getConstants", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "ObjectTypeAnnotation", "properties": [{"name": "initialWindowMetrics", "optional": true, "typeAnnotation": {"type": "ObjectTypeAnnotation", "properties": [{"name": "insets", "optional": false, "typeAnnotation": {"type": "ObjectTypeAnnotation", "properties": [{"name": "top", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}, {"name": "right", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}, {"name": "bottom", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}, {"name": "left", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}]}}, {"name": "frame", "optional": false, "typeAnnotation": {"type": "ObjectTypeAnnotation", "properties": [{"name": "x", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}, {"name": "y", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}, {"name": "width", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}, {"name": "height", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}]}}]}}]}, "params": []}}]}, "moduleName": "RNCSafeAreaContext"}, "RNCSafeAreaProvider": {"type": "Component", "components": {"RNCSafeAreaProvider": {"extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [{"name": "onInsetsChange", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": [{"name": "insets", "optional": false, "typeAnnotation": {"type": "ObjectTypeAnnotation", "properties": [{"name": "top", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}, {"name": "right", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}, {"name": "bottom", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}, {"name": "left", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}]}}, {"name": "frame", "optional": false, "typeAnnotation": {"type": "ObjectTypeAnnotation", "properties": [{"name": "x", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}, {"name": "y", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}, {"name": "width", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}, {"name": "height", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}]}}]}}}], "props": [], "commands": []}}}, "RNCSafeAreaView": {"type": "Component", "components": {"RNCSafeAreaView": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "mode", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "padding", "options": ["padding", "margin"]}}, {"name": "edges", "optional": true, "typeAnnotation": {"type": "ObjectTypeAnnotation", "properties": [{"name": "top", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "right", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "bottom", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "left", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}]}}], "commands": []}}}}}