{"version": 3, "names": ["printRunDoctorTip", "linkToDocs", "logger", "log", "info", "chalk", "dim", "reset", "bold"], "sources": ["../src/printRunDoctorTip.ts"], "sourcesContent": ["import logger from './logger';\nimport chalk from 'chalk';\n\nconst printRunDoctorTip = () => {\n  const linkToDocs =\n    'https://github.com/react-native-community/cli/blob/main/packages/cli-doctor/README.md#doctor';\n\n  logger.log('');\n  logger.info(\n    chalk.dim(\n      `${chalk.dim(\n        '💡 Tip: Make sure that you have set up your development environment correctly, by running',\n      )} ${chalk.reset(chalk.bold('npx react-native doctor'))}. ${chalk.dim(\n        `To read more about doctor command visit: ${linkToDocs} \\n`,\n      )}`,\n    ),\n  );\n};\n\nexport default printRunDoctorTip;\n"], "mappings": ";;;;;;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0B;AAE1B,MAAMA,iBAAiB,GAAG,MAAM;EAC9B,MAAMC,UAAU,GACd,8FAA8F;EAEhGC,eAAM,CAACC,GAAG,CAAC,EAAE,CAAC;EACdD,eAAM,CAACE,IAAI,CACTC,gBAAK,CAACC,GAAG,CACN,GAAED,gBAAK,CAACC,GAAG,CACV,2FAA2F,CAC3F,IAAGD,gBAAK,CAACE,KAAK,CAACF,gBAAK,CAACG,IAAI,CAAC,yBAAyB,CAAC,CAAE,KAAIH,gBAAK,CAACC,GAAG,CAClE,4CAA2CL,UAAW,KAAI,CAC3D,EAAC,CACJ,CACF;AACH,CAAC;AAAC,eAEaD,iBAAiB;AAAA"}