ninja: Entering directory `D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\.cxx\Debug\2e4v5m5x\x86'
[0/2] Re-checking globbed directories...
[1/5] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o
[2/5] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o
[3/5] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o
[4/5] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o
[5/5] Linking CXX shared library "D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\cxx\Debug\2e4v5m5x\obj\x86\libappmodules.so"
