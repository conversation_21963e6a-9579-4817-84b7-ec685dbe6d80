import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { formatDate, getExpirationStatus, getCategoryIcon, getCategoryColor } from '../utils/dateUtils';
import { itemsAPI } from '../services/api';

const ItemDetailScreen = ({ navigation, route }) => {
  const { item } = route.params;
  const expirationStatus = getExpirationStatus(item.expiration_date);
  const categoryColor = getCategoryColor(item.category);

  const handleDelete = () => {
    Alert.alert(
      'Delete Item',
      'Are you sure you want to delete this item?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await itemsAPI.delete(item.id);
              Alert.alert('Success', 'Item deleted successfully');
              navigation.goBack();
            } catch (error) {
              console.error('Error deleting item:', error);
              Alert.alert('Error', 'Failed to delete item');
            }
          },
        },
      ]
    );
  };

  const DetailRow = ({ label, value, icon }) => (
    <View style={styles.detailRow}>
      <View style={styles.detailLabel}>
        <Icon name={icon} size={20} color="#6b7280" />
        <Text style={styles.detailLabelText}>{label}</Text>
      </View>
      <Text style={styles.detailValue}>{value}</Text>
    </View>
  );

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.itemTitleContainer}>
          <Text style={styles.categoryIcon}>{getCategoryIcon(item.category)}</Text>
          <View style={styles.itemInfo}>
            <Text style={styles.itemName}>{item.item_name}</Text>
            <View style={[styles.categoryBadge, { backgroundColor: categoryColor }]}>
              <Text style={styles.categoryText}>{item.category}</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Expiration Status */}
      <View style={[styles.statusCard, { backgroundColor: expirationStatus.backgroundColor }]}>
        <View style={styles.statusHeader}>
          <Icon 
            name={expirationStatus.status === 'expired' ? 'warning' : 'schedule'} 
            size={24} 
            color={expirationStatus.textColor} 
          />
          <Text style={[styles.statusTitle, { color: expirationStatus.textColor }]}>
            Expiration Status
          </Text>
        </View>
        <Text style={[styles.statusMessage, { color: expirationStatus.textColor }]}>
          {expirationStatus.message}
        </Text>
        <Text style={[styles.statusDate, { color: expirationStatus.textColor }]}>
          Expires: {formatDate(item.expiration_date)}
        </Text>
      </View>

      {/* Item Details */}
      <View style={styles.detailsCard}>
        <Text style={styles.sectionTitle}>Item Details</Text>
        
        <DetailRow
          label="Item Name"
          value={item.item_name}
          icon="inventory"
        />
        
        <DetailRow
          label="Category"
          value={item.category}
          icon="category"
        />
        
        <DetailRow
          label="Expiration Date"
          value={formatDate(item.expiration_date)}
          icon="event"
        />
        
        <DetailRow
          label="Quantity"
          value={item.quantity || 1}
          icon="numbers"
        />
        
        {item.location && (
          <DetailRow
            label="Location"
            value={item.location}
            icon="place"
          />
        )}
        
        {item.notes && (
          <DetailRow
            label="Notes"
            value={item.notes}
            icon="note"
          />
        )}
        
        <DetailRow
          label="Date Added"
          value={formatDate(item.created_date)}
          icon="schedule"
        />
        
        {item.updated_date && item.updated_date !== item.created_date && (
          <DetailRow
            label="Last Updated"
            value={formatDate(item.updated_date)}
            icon="update"
          />
        )}
      </View>

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        <TouchableOpacity
          style={[styles.actionButton, styles.editButton]}
          onPress={() => navigation.navigate('AddItem', { item })}
        >
          <Icon name="edit" size={20} color="#fff" />
          <Text style={styles.actionButtonText}>Edit Item</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={handleDelete}
        >
          <Icon name="delete" size={20} color="#fff" />
          <Text style={styles.actionButtonText}>Delete Item</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  itemTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryIcon: {
    fontSize: 32,
    marginRight: 16,
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  categoryBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
  },
  categoryText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  statusCard: {
    margin: 20,
    padding: 20,
    borderRadius: 12,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  statusMessage: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  statusDate: {
    fontSize: 14,
    opacity: 0.8,
  },
  detailsCard: {
    backgroundColor: '#fff',
    margin: 20,
    marginTop: 0,
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  detailLabel: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  detailLabelText: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 8,
    fontWeight: '500',
  },
  detailValue: {
    fontSize: 14,
    color: '#1f2937',
    fontWeight: '600',
    flex: 1,
    textAlign: 'right',
  },
  actionContainer: {
    padding: 20,
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 8,
    gap: 8,
  },
  editButton: {
    backgroundColor: '#2563eb',
  },
  deleteButton: {
    backgroundColor: '#dc2626',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ItemDetailScreen;
