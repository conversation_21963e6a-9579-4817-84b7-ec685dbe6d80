import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  Modal,
  Image,
  Platform,
  PermissionsAndroid,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { launchImageLibrary, launchCamera } from 'react-native-image-picker';
import { addNewItem, updateItem } from '../data/mockData';

const AddItemScreen = ({ navigation, route }) => {
  const [formData, setFormData] = useState({
    item_name: '',
    category: '',
    expiration_date: '',
    notes: '',
    quantity: '1',
    location: '',
    image: null,
  });
  const [loading, setLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [showCategoryPicker, setShowCategoryPicker] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showImagePicker, setShowImagePicker] = useState(false);

  // الفئات الجديدة حسب الطلب
  const categories = [
    { value: 'medicine', label: 'Medicine', icon: '💊' },
    { value: 'mom&baby', label: 'Mom&baby', icon: '👶' },
    { value: 'personal_care', label: 'Personal care', icon: '🧴' },
    { value: 'nutraceutical', label: 'Nutraceutical', icon: '🌿' },
    { value: 'beauty', label: 'Beauty', icon: '💄' }
  ];

  useEffect(() => {
    if (route?.params?.item) {
      const item = route.params.item;
      setIsEditing(true);
      setFormData({
        item_name: item.item_name || '',
        category: item.category || '',
        expiration_date: item.expiration_date || '',
        notes: item.notes || '',
        quantity: (item.quantity || 1).toString(),
        location: item.location || '',
        image: item.image || null,
      });
    }
  }, [route?.params?.item]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const validateForm = () => {
    if (!formData.item_name.trim()) {
      Alert.alert('خطأ في التحقق', 'اسم المنتج مطلوب');
      return false;
    }
    if (!formData.category) {
      Alert.alert('خطأ في التحقق', 'يرجى اختيار فئة');
      return false;
    }
    if (!formData.expiration_date) {
      Alert.alert('خطأ في التحقق', 'تاريخ انتهاء الصلاحية مطلوب');
      return false;
    }
    if (isNaN(parseInt(formData.quantity)) || parseInt(formData.quantity) < 1) {
      Alert.alert('خطأ في التحقق', 'الكمية يجب أن تكون رقماً موجباً');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const submitData = {
        ...formData,
        quantity: parseInt(formData.quantity),
      };

      if (isEditing) {
        await updateItem(route.params.item.id, submitData);
        Alert.alert('نجح', 'تم تحديث المنتج بنجاح', [
          {
            text: 'موافق',
            onPress: () => {
              // العودة للشاشة السابقة
              if (navigation.canGoBack()) {
                navigation.goBack();
              } else {
                // إذا لم نتمكن من العودة، اذهب للوحة التحكم
                navigation.navigate('Dashboard');
              }
            }
          }
        ]);
      } else {
        await addNewItem(submitData);
        Alert.alert('نجح', 'تم إضافة المنتج بنجاح', [
          {
            text: 'موافق',
            onPress: () => {
              // مسح النموذج للمنتج الجديد
              setFormData({
                item_name: '',
                category: '',
                expiration_date: '',
                notes: '',
                quantity: '1',
                location: '',
                image: null,
              });

              // العودة للوحة التحكم أو الشاشة السابقة
              if (navigation.canGoBack()) {
                navigation.goBack();
              } else {
                navigation.navigate('Dashboard');
              }
            }
          }
        ]);
      }
    } catch (error) {
      console.error('خطأ في حفظ المنتج:', error);
      Alert.alert('خطأ', 'فشل في حفظ المنتج. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  const handleDateChange = (text) => {
    // تنسيق بسيط للتاريخ YYYY-MM-DD
    let formatted = text.replace(/[^0-9]/g, '');
    if (formatted.length >= 4) {
      formatted = formatted.substring(0, 4) + '-' + formatted.substring(4);
    }
    if (formatted.length >= 7) {
      formatted = formatted.substring(0, 7) + '-' + formatted.substring(7, 9);
    }
    if (formatted.length > 10) {
      formatted = formatted.substring(0, 10);
    }
    handleInputChange('expiration_date', formatted);
  };

  // دالة التعامل مع DateTimePicker
  const onDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      const formattedDate = selectedDate.toISOString().split('T')[0];
      handleInputChange('expiration_date', formattedDate);
    }
  };

  // طلب إذن الكاميرا
  const requestCameraPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: 'إذن الكاميرا',
            message: 'يحتاج التطبيق إلى إذن الكاميرا لالتقاط الصور',
            buttonNeutral: 'اسأل لاحقاً',
            buttonNegative: 'إلغاء',
            buttonPositive: 'موافق',
          },
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn(err);
        return false;
      }
    }
    return true;
  };

  // دالة التقاط الصورة من الكاميرا
  const takePhoto = async () => {
    const hasPermission = await requestCameraPermission();
    if (!hasPermission) {
      Alert.alert('خطأ', 'إذن الكاميرا مطلوب لالتقاط الصور');
      return;
    }

    const options = {
      mediaType: 'photo',
      quality: 0.8,
      maxWidth: 800,
      maxHeight: 600,
    };

    launchCamera(options, (response) => {
      if (response.didCancel || response.error) {
        return;
      }
      if (response.assets && response.assets[0]) {
        handleInputChange('image', response.assets[0]);
      }
      setShowImagePicker(false);
    });
  };

  // دالة اختيار الصورة من المعرض
  const pickImage = () => {
    const options = {
      mediaType: 'photo',
      quality: 0.8,
      maxWidth: 800,
      maxHeight: 600,
    };

    launchImageLibrary(options, (response) => {
      if (response.didCancel || response.error) {
        return;
      }
      if (response.assets && response.assets[0]) {
        handleInputChange('image', response.assets[0]);
      }
      setShowImagePicker(false);
    });
  };

  const CategoryPicker = () => (
    <Modal
      visible={showCategoryPicker}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setShowCategoryPicker(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowCategoryPicker(false)}>
              <Icon name="close" size={24} color="#6b7280" />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>اختر الفئة</Text>
          </View>
          {categories.map((category) => (
            <TouchableOpacity
              key={category.value}
              style={styles.categoryOption}
              onPress={() => {
                handleInputChange('category', category.value);
                setShowCategoryPicker(false);
              }}
            >
              <Text style={styles.categoryOptionText}>
                {category.icon} {category.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </Modal>
  );

  const selectedCategory = categories.find(cat => cat.value === formData.category);

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {isEditing ? 'تعديل المنتج' : 'إضافة منتج جديد'}
        </Text>
        <Text style={styles.headerSubtitle}>
          {isEditing ? 'تحديث معلومات المنتج' : 'أضف منتجاً جديداً إلى مخزونك'}
        </Text>
      </View>

      <View style={styles.form}>
        {/* اسم المنتج */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>اسم المنتج *</Text>
          <TextInput
            style={styles.input}
            value={formData.item_name}
            onChangeText={(text) => handleInputChange('item_name', text)}
            placeholder="أدخل اسم المنتج"
            placeholderTextColor="#9ca3af"
            textAlign="right"
          />
        </View>

        {/* الفئة */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>الفئة *</Text>
          <TouchableOpacity
            style={styles.pickerButton}
            onPress={() => setShowCategoryPicker(true)}
          >
            <Icon name="expand-more" size={20} color="#6b7280" />
            <Text style={[styles.pickerText, !selectedCategory && styles.placeholderText]}>
              {selectedCategory ? `${selectedCategory.icon} ${selectedCategory.label}` : 'اختر الفئة'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* صورة المنتج */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>صورة المنتج</Text>
          <TouchableOpacity
            style={styles.imageButton}
            onPress={() => setShowImagePicker(true)}
          >
            {formData.image ? (
              <Image source={{ uri: formData.image.uri }} style={styles.imagePreview} />
            ) : (
              <View style={styles.imagePlaceholder}>
                <Icon name="camera-alt" size={40} color="#9ca3af" />
                <Text style={styles.imagePlaceholderText}>اضغط لإضافة صورة</Text>
              </View>
            )}
          </TouchableOpacity>
        </View>

        {/* تاريخ انتهاء الصلاحية */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>تاريخ انتهاء الصلاحية *</Text>
          <View style={styles.dateInputContainer}>
            <TouchableOpacity
              style={styles.datePickerButton}
              onPress={() => setShowDatePicker(true)}
            >
              <Icon name="calendar-today" size={20} color="#6b7280" />
            </TouchableOpacity>
            <TextInput
              style={[styles.input, styles.dateInput]}
              value={formData.expiration_date}
              onChangeText={handleDateChange}
              placeholder="YYYY-MM-DD"
              placeholderTextColor="#9ca3af"
              keyboardType="numeric"
              maxLength={10}
              textAlign="right"
            />
          </View>
          <Text style={styles.helpText}>التنسيق: YYYY-MM-DD (مثال: 2024-12-31)</Text>
        </View>

        {/* الكمية والموقع */}
        <View style={styles.row}>
          <View style={[styles.inputGroup, styles.halfWidth]}>
            <Text style={styles.label}>الموقع</Text>
            <TextInput
              style={styles.input}
              value={formData.location}
              onChangeText={(text) => handleInputChange('location', text)}
              placeholder="مثال: الرف أ"
              placeholderTextColor="#9ca3af"
              textAlign="right"
            />
          </View>

          <View style={[styles.inputGroup, styles.halfWidth]}>
            <Text style={styles.label}>الكمية</Text>
            <TextInput
              style={styles.input}
              value={formData.quantity}
              onChangeText={(text) => handleInputChange('quantity', text)}
              placeholder="1"
              placeholderTextColor="#9ca3af"
              keyboardType="numeric"
              textAlign="right"
            />
          </View>
        </View>

        {/* الملاحظات */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>ملاحظات</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={formData.notes}
            onChangeText={(text) => handleInputChange('notes', text)}
            placeholder="ملاحظات إضافية حول هذا المنتج"
            placeholderTextColor="#9ca3af"
            multiline
            numberOfLines={3}
            textAlignVertical="top"
            textAlign="right"
          />
        </View>

        {/* أزرار الإجراءات */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.primaryButton]}
            onPress={handleSubmit}
            disabled={loading}
          >
            <Icon name="save" size={20} color="#fff" />
            <Text style={styles.primaryButtonText}>
              {loading ? 'جاري الحفظ...' : (isEditing ? 'تحديث المنتج' : 'إضافة المنتج')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.secondaryButton]}
            onPress={() => {
              if (navigation.canGoBack()) {
                navigation.goBack();
              } else {
                navigation.navigate('Dashboard');
              }
            }}
            disabled={loading}
          >
            <Text style={styles.secondaryButtonText}>إلغاء</Text>
          </TouchableOpacity>
        </View>
      </View>

      <CategoryPicker />

      {/* DateTimePicker */}
      {showDatePicker && (
        <DateTimePicker
          value={formData.expiration_date ? new Date(formData.expiration_date) : new Date()}
          mode="date"
          display="default"
          onChange={onDateChange}
        />
      )}

      {/* Image Picker Modal */}
      <Modal
        visible={showImagePicker}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowImagePicker(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <TouchableOpacity onPress={() => setShowImagePicker(false)}>
                <Icon name="close" size={24} color="#6b7280" />
              </TouchableOpacity>
              <Text style={styles.modalTitle}>اختر مصدر الصورة</Text>
            </View>

            <TouchableOpacity style={styles.imageOption} onPress={takePhoto}>
              <Icon name="camera-alt" size={24} color="#2563eb" />
              <Text style={styles.imageOptionText}>التقاط صورة</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.imageOption} onPress={pickImage}>
              <Icon name="photo-library" size={24} color="#2563eb" />
              <Text style={styles.imageOptionText}>اختيار من المعرض</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  form: {
    padding: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
    textAlign: 'right',
  },
  input: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
    color: '#1f2937',
  },
  textArea: {
    height: 80,
    paddingTop: 12,
  },
  pickerButton: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pickerText: {
    fontSize: 16,
    color: '#1f2937',
  },
  placeholderText: {
    color: '#9ca3af',
  },
  helpText: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
    textAlign: 'right',
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  buttonContainer: {
    marginTop: 20,
    gap: 12,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 8,
    gap: 8,
  },
  primaryButton: {
    backgroundColor: '#2563eb',
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  secondaryButtonText: {
    color: '#374151',
    fontSize: 16,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    width: '80%',
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  categoryOption: {
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  categoryOptionText: {
    fontSize: 16,
    color: '#1f2937',
    textAlign: 'right',
  },
  // أنماط الصورة
  imageButton: {
    borderWidth: 2,
    borderColor: '#e5e7eb',
    borderStyle: 'dashed',
    borderRadius: 8,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f9fafb',
  },
  imagePlaceholder: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  imagePlaceholderText: {
    fontSize: 14,
    color: '#9ca3af',
    marginTop: 8,
    textAlign: 'center',
  },
  imagePreview: {
    width: 120,
    height: 120,
    borderRadius: 8,
    resizeMode: 'cover',
  },
  // أنماط التاريخ
  dateInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  dateInput: {
    flex: 1,
    borderWidth: 0,
    margin: 0,
  },
  datePickerButton: {
    padding: 12,
    borderRightWidth: 1,
    borderRightColor: '#e5e7eb',
  },
  // أنماط مودال الصورة
  imageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  imageOptionText: {
    fontSize: 16,
    color: '#1f2937',
    marginLeft: 12,
    textAlign: 'right',
    flex: 1,
  },
});

export default AddItemScreen;
