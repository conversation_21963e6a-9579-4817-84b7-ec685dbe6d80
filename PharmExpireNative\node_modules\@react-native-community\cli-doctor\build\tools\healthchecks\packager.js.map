{"version": 3, "names": ["label", "isRequired", "description", "getDiagnostics", "status", "isPackagerRunning", "needsToBeFixed", "runAutomaticFix", "loader", "config", "fail", "terminal", "getDefaultUserTerminal", "port", "Number", "process", "env", "RCT_METRO_PORT", "execa", "path", "join", "reactNativePath", "toString", "succeed", "logManualInstallation", "message", "error"], "sources": ["../../../src/tools/healthchecks/packager.ts"], "sourcesContent": ["import {\n  getDefaultUserTerminal,\n  isPackagerRunning,\n} from '@react-native-community/cli-tools';\nimport {HealthCheckInterface} from '../../types';\nimport {logManualInstallation} from './common';\nimport execa from 'execa';\nimport path from 'path';\n\nexport default {\n  label: 'Metro',\n  isRequired: false,\n  description: 'Required for bundling the JavaScript code',\n  getDiagnostics: async () => {\n    const status = await isPackagerRunning();\n    const needsToBeFixed = status === 'not_running';\n    if (needsToBeFixed) {\n      return {\n        description: 'Metro Bundler is not running',\n        needsToBeFixed,\n      };\n    }\n    return {\n      needsToBeFixed,\n    };\n  },\n  runAutomaticFix: async ({loader, config}) => {\n    loader.fail();\n    try {\n      const terminal = getDefaultUserTerminal();\n      const port = Number(process.env.RCT_METRO_PORT) || 8081;\n      if (terminal && config) {\n        await execa('node', [\n          path.join(config.reactNativePath, 'cli.js'),\n          'start',\n          '--port',\n          port.toString(),\n          '--terminal',\n          terminal,\n        ]);\n        return loader.succeed();\n      }\n      return logManualInstallation({\n        message:\n          'Could not start the bundler. Please run \"npx react-native start\" command manually.',\n      });\n    } catch (error) {\n      return logManualInstallation({\n        message:\n          'Could not start the bundler. Please run \"npx react-native start\" command manually.',\n      });\n    }\n  },\n} as HealthCheckInterface;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAKA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AAAA,eAET;EACbA,KAAK,EAAE,OAAO;EACdC,UAAU,EAAE,KAAK;EACjBC,WAAW,EAAE,2CAA2C;EACxDC,cAAc,EAAE,YAAY;IAC1B,MAAMC,MAAM,GAAG,MAAM,IAAAC,6BAAiB,GAAE;IACxC,MAAMC,cAAc,GAAGF,MAAM,KAAK,aAAa;IAC/C,IAAIE,cAAc,EAAE;MAClB,OAAO;QACLJ,WAAW,EAAE,8BAA8B;QAC3CI;MACF,CAAC;IACH;IACA,OAAO;MACLA;IACF,CAAC;EACH,CAAC;EACDC,eAAe,EAAE,OAAO;IAACC,MAAM;IAAEC;EAAM,CAAC,KAAK;IAC3CD,MAAM,CAACE,IAAI,EAAE;IACb,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAAC,kCAAsB,GAAE;MACzC,MAAMC,IAAI,GAAGC,MAAM,CAACC,OAAO,CAACC,GAAG,CAACC,cAAc,CAAC,IAAI,IAAI;MACvD,IAAIN,QAAQ,IAAIF,MAAM,EAAE;QACtB,MAAM,IAAAS,gBAAK,EAAC,MAAM,EAAE,CAClBC,eAAI,CAACC,IAAI,CAACX,MAAM,CAACY,eAAe,EAAE,QAAQ,CAAC,EAC3C,OAAO,EACP,QAAQ,EACRR,IAAI,CAACS,QAAQ,EAAE,EACf,YAAY,EACZX,QAAQ,CACT,CAAC;QACF,OAAOH,MAAM,CAACe,OAAO,EAAE;MACzB;MACA,OAAO,IAAAC,6BAAqB,EAAC;QAC3BC,OAAO,EACL;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,OAAO,IAAAF,6BAAqB,EAAC;QAC3BC,OAAO,EACL;MACJ,CAAC,CAAC;IACJ;EACF;AACF,CAAC;AAAA"}