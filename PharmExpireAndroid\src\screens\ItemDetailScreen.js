import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { formatDate, getExpirationStatus, getCategoryIcon, getCategoryColor } from '../utils/dateUtils';
import { itemsAPI } from '../services/api';

const ItemDetailScreen = ({ navigation, route }) => {
  const { item } = route.params;
  const expirationStatus = getExpirationStatus(item.expiration_date);
  const categoryColor = getCategoryColor(item.category);

  const handleDelete = () => {
    Alert.alert(
      'حذف المنتج',
      'هل أنت متأكد من حذف هذا المنتج؟',
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'حذف',
          style: 'destructive',
          onPress: async () => {
            try {
              await itemsAPI.delete(item.id);
              Alert.alert('نجح', 'تم حذف المنتج بنجاح');
              navigation.goBack();
            } catch (error) {
              console.error('خطأ في حذف المنتج:', error);
              Alert.alert('خطأ', 'فشل في حذف المنتج');
            }
          },
        },
      ]
    );
  };

  const DetailRow = ({ label, value, icon }) => (
    <View style={styles.detailRow}>
      <Text style={styles.detailValue}>{value}</Text>
      <View style={styles.detailLabel}>
        <Text style={styles.detailLabelText}>{label}</Text>
        <Ionicons name={icon} size={20} color="#6b7280" />
      </View>
    </View>
  );

  return (
    <ScrollView style={styles.container}>
      {/* العنوان الرئيسي */}
      <View style={styles.header}>
        <View style={styles.itemTitleContainer}>
          <View style={styles.itemInfo}>
            <Text style={styles.itemName}>{item.item_name}</Text>
            <View style={[styles.categoryBadge, { backgroundColor: categoryColor }]}>
              <Text style={styles.categoryText}>{item.category}</Text>
            </View>
          </View>
          <Text style={styles.categoryIcon}>{getCategoryIcon(item.category)}</Text>
        </View>
      </View>

      {/* حالة انتهاء الصلاحية */}
      <View style={[styles.statusCard, { backgroundColor: expirationStatus.backgroundColor }]}>
        <View style={styles.statusHeader}>
          <Text style={[styles.statusTitle, { color: expirationStatus.textColor }]}>
            حالة انتهاء الصلاحية
          </Text>
          <Ionicons 
            name={expirationStatus.status === 'expired' ? 'warning' : 'time'} 
            size={24} 
            color={expirationStatus.textColor} 
          />
        </View>
        <Text style={[styles.statusMessage, { color: expirationStatus.textColor }]}>
          {expirationStatus.message}
        </Text>
        <Text style={[styles.statusDate, { color: expirationStatus.textColor }]}>
          تاريخ الانتهاء: {formatDate(item.expiration_date)}
        </Text>
      </View>

      {/* تفاصيل المنتج */}
      <View style={styles.detailsCard}>
        <Text style={styles.sectionTitle}>تفاصيل المنتج</Text>
        
        <DetailRow
          label="اسم المنتج"
          value={item.item_name}
          icon="cube"
        />
        
        <DetailRow
          label="الفئة"
          value={item.category}
          icon="apps"
        />
        
        <DetailRow
          label="تاريخ انتهاء الصلاحية"
          value={formatDate(item.expiration_date)}
          icon="calendar"
        />
        
        <DetailRow
          label="الكمية"
          value={item.quantity || 1}
          icon="layers"
        />
        
        {item.location && (
          <DetailRow
            label="الموقع"
            value={item.location}
            icon="location"
          />
        )}
        
        {item.notes && (
          <DetailRow
            label="ملاحظات"
            value={item.notes}
            icon="document-text"
          />
        )}
        
        <DetailRow
          label="تاريخ الإضافة"
          value={formatDate(item.created_date)}
          icon="time"
        />
        
        {item.updated_date && item.updated_date !== item.created_date && (
          <DetailRow
            label="آخر تحديث"
            value={formatDate(item.updated_date)}
            icon="refresh"
          />
        )}
      </View>

      {/* أزرار الإجراءات */}
      <View style={styles.actionContainer}>
        <TouchableOpacity
          style={[styles.actionButton, styles.editButton]}
          onPress={() => navigation.navigate('AddItem', { item })}
        >
          <Ionicons name="create" size={20} color="#fff" />
          <Text style={styles.actionButtonText}>تعديل المنتج</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={handleDelete}
        >
          <Ionicons name="trash" size={20} color="#fff" />
          <Text style={styles.actionButtonText}>حذف المنتج</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  itemTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryIcon: {
    fontSize: 32,
    marginLeft: 16,
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
    textAlign: 'right',
  },
  categoryBadge: {
    alignSelf: 'flex-end',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
  },
  categoryText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  statusCard: {
    margin: 20,
    padding: 20,
    borderRadius: 12,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  statusMessage: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
    textAlign: 'right',
  },
  statusDate: {
    fontSize: 14,
    opacity: 0.8,
    textAlign: 'right',
  },
  detailsCard: {
    backgroundColor: '#fff',
    margin: 20,
    marginTop: 0,
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
    textAlign: 'right',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  detailLabel: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  detailLabelText: {
    fontSize: 14,
    color: '#6b7280',
    marginRight: 8,
    fontWeight: '500',
  },
  detailValue: {
    fontSize: 14,
    color: '#1f2937',
    fontWeight: '600',
    flex: 1,
    textAlign: 'left',
  },
  actionContainer: {
    padding: 20,
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 8,
    gap: 8,
  },
  editButton: {
    backgroundColor: '#2563eb',
  },
  deleteButton: {
    backgroundColor: '#dc2626',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ItemDetailScreen;
