{"version": 3, "names": ["brewInstall", "pkg", "label", "loader", "onSuccess", "onFail", "start", "execa", "succeed", "error", "logError", "healthcheck", "command"], "sources": ["../../src/tools/brewInstall.ts"], "sourcesContent": ["import execa from 'execa';\nimport {Loader} from '../types';\nimport {logError} from './healthchecks/common';\n\ntype InstallArgs = {\n  pkg: string;\n  label?: string;\n  loader: Loader;\n  onSuccess?: () => void;\n  onFail?: () => void;\n};\n\nasync function brewInstall({\n  pkg,\n  label,\n  loader,\n  onSuccess,\n  onFail,\n}: InstallArgs) {\n  loader.start(label);\n\n  try {\n    await execa('brew', ['install', pkg]);\n\n    if (typeof onSuccess === 'function') {\n      return onSuccess();\n    }\n\n    return loader.succeed();\n  } catch (error) {\n    if (typeof onFail === 'function') {\n      return onFail();\n    }\n\n    logError({\n      healthcheck: label || pkg,\n      loader,\n      error: error as any,\n      command: `brew install ${pkg}`,\n    });\n  }\n}\n\nexport {brewInstall};\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AAA+C;AAU/C,eAAeA,WAAW,CAAC;EACzBC,GAAG;EACHC,KAAK;EACLC,MAAM;EACNC,SAAS;EACTC;AACW,CAAC,EAAE;EACdF,MAAM,CAACG,KAAK,CAACJ,KAAK,CAAC;EAEnB,IAAI;IACF,MAAM,IAAAK,gBAAK,EAAC,MAAM,EAAE,CAAC,SAAS,EAAEN,GAAG,CAAC,CAAC;IAErC,IAAI,OAAOG,SAAS,KAAK,UAAU,EAAE;MACnC,OAAOA,SAAS,EAAE;IACpB;IAEA,OAAOD,MAAM,CAACK,OAAO,EAAE;EACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,IAAI,OAAOJ,MAAM,KAAK,UAAU,EAAE;MAChC,OAAOA,MAAM,EAAE;IACjB;IAEA,IAAAK,gBAAQ,EAAC;MACPC,WAAW,EAAET,KAAK,IAAID,GAAG;MACzBE,MAAM;MACNM,KAAK,EAAEA,KAAY;MACnBG,OAAO,EAAG,gBAAeX,GAAI;IAC/B,CAAC,CAAC;EACJ;AACF"}