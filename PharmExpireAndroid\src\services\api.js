import axios from 'axios';

// عنوان IP الخاص بجهازك - قم بتغييره حسب عنوان IP الخاص بك
const API_BASE_URL = 'http://************:3001/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000,
});

// خدمات API للمنتجات
export const itemsAPI = {
  // الحصول على جميع المنتجات
  getAll: () => api.get('/items'),
  
  // الحصول على منتج بواسطة ID
  getById: (id) => api.get(`/items/${id}`),
  
  // الحصول على المنتجات التي تنتهي قريباً
  getExpiringSoon: (days = 7) => api.get(`/items/filter/expiring-soon?days=${days}`),
  
  // الحصول على المنتجات المنتهية
  getExpired: () => api.get('/items/filter/expired'),
  
  // الحصول على المنتجات بواسطة الفئة
  getByCategory: (category) => api.get(`/items/category/${category}`),
  
  // الحصول على إحصائيات لوحة التحكم
  getStats: () => api.get('/items/stats/dashboard'),
  
  // إنشاء منتج جديد
  create: (itemData) => api.post('/items', itemData),
  
  // تحديث منتج
  update: (id, itemData) => api.put(`/items/${id}`, itemData),
  
  // حذف منتج
  delete: (id) => api.delete(`/items/${id}`),
};

// فحص صحة الخدمة
export const healthCheck = () => api.get('/health');

export default api;
