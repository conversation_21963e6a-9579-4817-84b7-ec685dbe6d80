import React, { useState, useEffect } from 'react';
import { Search, Filter, Edit, Trash2, Package, AlertTriangle, Calendar, Plus } from 'lucide-react';
import { itemsAPI } from '../services/api';
import { formatDate, getExpirationStatus, getCategoryIcon, getCategoryColor } from '../utils/dateUtils';

const ItemList = ({ filter = 'all', onEdit, onAdd }) => {
  const [items, setItems] = useState([]);
  const [filteredItems, setFilteredItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [sortBy, setSortBy] = useState('expiration_date');

  const categories = ['Food', 'Medicine', 'Mom & Baby', 'Cosmetic', 'Wellness'];

  useEffect(() => {
    fetchItems();
  }, [filter]);

  useEffect(() => {
    filterAndSortItems();
  }, [items, searchTerm, categoryFilter, sortBy]);

  const fetchItems = async () => {
    try {
      setLoading(true);
      let response;
      
      switch (filter) {
        case 'expiring':
          response = await itemsAPI.getExpiringSoon();
          break;
        case 'expired':
          response = await itemsAPI.getExpired();
          break;
        default:
          response = await itemsAPI.getAll();
      }
      
      setItems(response.data);
    } catch (err) {
      setError('Failed to load items');
      console.error('Error fetching items:', err);
    } finally {
      setLoading(false);
    }
  };

  const filterAndSortItems = () => {
    let filtered = [...items];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.item_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.notes?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.location?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Category filter
    if (categoryFilter) {
      filtered = filtered.filter(item => item.category === categoryFilter);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.item_name.localeCompare(b.item_name);
        case 'category':
          return a.category.localeCompare(b.category);
        case 'expiration_date':
          return new Date(a.expiration_date) - new Date(b.expiration_date);
        case 'created_date':
          return new Date(b.created_date) - new Date(a.created_date);
        default:
          return 0;
      }
    });

    setFilteredItems(filtered);
  };

  const handleDelete = async (id) => {
    if (!window.confirm('Are you sure you want to delete this item?')) {
      return;
    }

    try {
      await itemsAPI.delete(id);
      setItems(items.filter(item => item.id !== id));
    } catch (err) {
      alert('Failed to delete item');
      console.error('Error deleting item:', err);
    }
  };

  const getTitle = () => {
    switch (filter) {
      case 'expiring':
        return 'Items Expiring Soon';
      case 'expired':
        return 'Expired Items';
      default:
        return 'All Items';
    }
  };

  const getIcon = () => {
    switch (filter) {
      case 'expiring':
        return Calendar;
      case 'expired':
        return AlertTriangle;
      default:
        return Package;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center">
          <AlertTriangle className="h-5 w-5 text-red-400 mr-2" />
          <p className="text-red-800">{error}</p>
        </div>
      </div>
    );
  }

  const Icon = getIcon();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-3">
          <div className={`p-2 rounded-lg ${
            filter === 'expired' ? 'bg-red-100' : 
            filter === 'expiring' ? 'bg-yellow-100' : 'bg-blue-100'
          }`}>
            <Icon className={`h-6 w-6 ${
              filter === 'expired' ? 'text-red-600' : 
              filter === 'expiring' ? 'text-yellow-600' : 'text-blue-600'
            }`} />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{getTitle()}</h1>
            <p className="text-gray-600 mt-1">
              {filteredItems.length} item{filteredItems.length !== 1 ? 's' : ''} found
            </p>
          </div>
        </div>
        <button
          onClick={onAdd}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
        >
          <Plus className="h-4 w-4" />
          Add Item
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search items..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Category Filter */}
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>

          {/* Sort */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="expiration_date">Sort by Expiration</option>
            <option value="name">Sort by Name</option>
            <option value="category">Sort by Category</option>
            <option value="created_date">Sort by Date Added</option>
          </select>

          {/* Clear Filters */}
          {(searchTerm || categoryFilter) && (
            <button
              onClick={() => {
                setSearchTerm('');
                setCategoryFilter('');
              }}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Clear Filters
            </button>
          )}
        </div>
      </div>

      {/* Items Grid */}
      {filteredItems.length === 0 ? (
        <div className="text-center py-12">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No items found</h3>
          <p className="text-gray-600 mb-4">
            {searchTerm || categoryFilter 
              ? 'Try adjusting your filters or search terms'
              : 'Get started by adding your first item'
            }
          </p>
          <button
            onClick={onAdd}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg inline-flex items-center gap-2 transition-colors"
          >
            <Plus className="h-4 w-4" />
            Add First Item
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredItems.map((item) => {
            const expirationStatus = getExpirationStatus(item.expiration_date);
            const categoryColor = getCategoryColor(item.category);
            
            return (
              <div
                key={item.id}
                className={`bg-white rounded-lg border-2 ${expirationStatus.borderColor} p-6 hover:shadow-lg transition-shadow`}
              >
                {/* Item Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">{getCategoryIcon(item.category)}</span>
                    <div>
                      <h3 className="font-semibold text-gray-900 text-lg">{item.item_name}</h3>
                      <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium border ${categoryColor}`}>
                        {item.category}
                      </span>
                    </div>
                  </div>
                  <div className="flex gap-1">
                    <button
                      onClick={() => onEdit(item)}
                      className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(item.id)}
                      className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                {/* Expiration Status */}
                <div className={`${expirationStatus.bgColor} ${expirationStatus.textColor} px-3 py-2 rounded-lg mb-4`}>
                  <p className="text-sm font-medium">{expirationStatus.message}</p>
                  <p className="text-xs opacity-75">Expires: {formatDate(item.expiration_date)}</p>
                </div>

                {/* Item Details */}
                <div className="space-y-2 text-sm text-gray-600">
                  {item.quantity > 1 && (
                    <p><span className="font-medium">Quantity:</span> {item.quantity}</p>
                  )}
                  {item.location && (
                    <p><span className="font-medium">Location:</span> {item.location}</p>
                  )}
                  {item.notes && (
                    <p><span className="font-medium">Notes:</span> {item.notes}</p>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default ItemList;
