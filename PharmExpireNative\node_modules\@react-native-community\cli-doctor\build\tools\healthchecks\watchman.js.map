{"version": 3, "names": ["label", "isRequired", "description", "getDiagnostics", "Binaries", "needsToBeFixed", "Boolean", "Watchman", "version", "runAutomaticFix", "loader", "install", "pkg", "url"], "sources": ["../../../src/tools/healthchecks/watchman.ts"], "sourcesContent": ["import {install} from '../install';\nimport {HealthCheckInterface} from '../../types';\n\nconst label = 'Watchman';\n\nexport default {\n  label,\n  isRequired: false,\n  description:\n    'Used for watching changes in the filesystem when in development mode',\n  getDiagnostics: async ({Binaries}) => ({\n    needsToBeFixed: <PERSON><PERSON><PERSON>(Binaries.Watchman.version) === false,\n  }),\n  runAutomaticFix: async ({loader}) =>\n    await install({\n      pkg: 'watchman',\n      label,\n      url: 'https://facebook.github.io/watchman/docs/install.html',\n      loader,\n    }),\n} as HealthCheckInterface;\n"], "mappings": ";;;;;;AAAA;AAGA,MAAMA,KAAK,GAAG,UAAU;AAAC,eAEV;EACbA,KAAK;EACLC,UAAU,EAAE,KAAK;EACjBC,WAAW,EACT,sEAAsE;EACxEC,cAAc,EAAE,OAAO;IAACC;EAAQ,CAAC,MAAM;IACrCC,cAAc,EAAEC,OAAO,CAACF,QAAQ,CAACG,QAAQ,CAACC,OAAO,CAAC,KAAK;EACzD,CAAC,CAAC;EACFC,eAAe,EAAE,OAAO;IAACC;EAAM,CAAC,KAC9B,MAAM,IAAAC,gBAAO,EAAC;IACZC,GAAG,EAAE,UAAU;IACfZ,KAAK;IACLa,GAAG,EAAE,uDAAuD;IAC5DH;EACF,CAAC;AACL,CAAC;AAAA"}