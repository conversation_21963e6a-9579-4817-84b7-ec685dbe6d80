
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateShadowNodeH.js
 */

#pragma once

#include <react/renderer/components/rngesturehandler_codegen/EventEmitters.h>
#include <react/renderer/components/rngesturehandler_codegen/Props.h>
#include <react/renderer/components/rngesturehandler_codegen/States.h>
#include <react/renderer/components/view/ConcreteViewShadowNode.h>
#include <jsi/jsi.h>

namespace facebook::react {

JSI_EXPORT extern const char RNGestureHandlerButtonComponentName[];

/*
 * `ShadowNode` for <RNGestureHandlerButton> component.
 */
using RNGestureHandlerButtonShadowNode = ConcreteViewShadowNode<
    RNGestureHandlerButtonComponentName,
    RNGestureHandlerButtonProps,
    RNGestureHandlerButtonEventEmitter,
    RNGestureHandlerButtonState>;

JSI_EXPORT extern const char RNGestureHandlerRootViewComponentName[];

/*
 * `ShadowNode` for <RNGestureHandlerRootView> component.
 */
using RNGestureHandlerRootViewShadowNode = ConcreteViewShadowNode<
    RNGestureHandlerRootViewComponentName,
    RNGestureHandlerRootViewProps,
    RNGestureHandlerRootViewEventEmitter,
    RNGestureHandlerRootViewState>;

} // namespace facebook::react
