# 💊 PharmExpire - Pharmacy Expire Management System

A comprehensive web application for tracking and managing pharmacy inventory expiration dates with color-coded alerts and category management.

![PharmExpire Dashboard](https://img.shields.io/badge/Status-Ready-green)
![React](https://img.shields.io/badge/React-18.x-blue)
![Node.js](https://img.shields.io/badge/Node.js-22.x-green)
![SQLite](https://img.shields.io/badge/Database-SQLite-blue)

## 🎯 Features

### ✨ Core Features
- **📊 Dashboard Overview** - Real-time statistics and quick actions
- **📦 Inventory Management** - Complete CRUD operations for items
- **🔔 Expiration Tracking** - Color-coded alerts (Red=Expired, Yellow=Expiring, Green=Safe)
- **📱 Responsive Design** - Works on desktop, tablet, and mobile devices
- **🔍 Advanced Search & Filtering** - Search by name, category, location, or notes
- **📈 Category Management** - Organized by Food, Medicine, Mom & Baby, Cosmetic, Wellness

### 🎨 Visual Indicators
- **🔴 Red**: Expired items
- **🟡 Yellow**: Items expiring within 7 days
- **🟢 Green**: Safe items (more than 7 days)

### 📋 Categories
- 🍎 **Food** - Food items and consumables
- 💊 **Medicine** - Medications and pharmaceuticals
- 👶 **Mom & Baby** - Baby care products
- 💄 **Cosmetic** - Beauty and skincare products
- 🌿 **Wellness** - Supplements and wellness products

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ installed
- npm or yarn package manager

### Installation

1. **Clone or navigate to the project directory**
   ```bash
   cd "d:\copy from kpi tracker app\expire management"
   ```

2. **Install Backend Dependencies**
   ```bash
   cd backend
   npm install
   ```

3. **Install Frontend Dependencies**
   ```bash
   cd ../frontend
   npm install
   ```

4. **Seed the Database (Optional)**
   ```bash
   cd ../backend
   npm run seed
   ```

### Running the Application

1. **Start the Backend Server**
   ```bash
   cd backend
   npm run dev
   ```
   Backend will run on: http://localhost:3001

2. **Start the Frontend Development Server**
   ```bash
   cd frontend
   npm run dev
   ```
   Frontend will run on: http://localhost:5173

3. **Open your browser and navigate to:**
   ```
   http://localhost:5173
   ```

## 📖 Usage Guide

### 🏠 Dashboard
- View overall statistics (total items, expired, expiring soon)
- See category breakdown
- Quick access to main functions

### ➕ Adding Items
1. Click "Add Item" button
2. Fill in the required information:
   - **Item Name** (required)
   - **Category** (required)
   - **Expiration Date** (required)
   - **Quantity** (optional, default: 1)
   - **Location** (optional)
   - **Notes** (optional)
3. Click "Add Item" to save

### 📝 Managing Items
- **View All Items**: See complete inventory
- **Edit**: Click the edit icon on any item card
- **Delete**: Click the trash icon (with confirmation)
- **Filter**: Use search and category filters
- **Sort**: Sort by expiration date, name, category, or date added

### 🔍 Filtering & Search
- **Search Bar**: Search by item name, notes, or location
- **Category Filter**: Filter by specific categories
- **Status Views**: 
  - All Items
  - Expiring Soon (within 7 days)
  - Expired Items

## 🏗️ Technical Architecture

### Backend (Node.js + Express)
```
backend/
├── server.js              # Main server file
├── database/
│   ├── db.js              # Database connection and setup
│   └── pharmacy.db        # SQLite database file
├── models/
│   └── Item.js            # Item data model
├── routes/
│   └── items.js           # API routes
├── seed-database.js       # Sample data seeder
└── package.json
```

### Frontend (React + Vite)
```
frontend/
├── src/
│   ├── components/
│   │   ├── Dashboard.jsx      # Main dashboard
│   │   ├── ItemForm.jsx       # Add/Edit form
│   │   └── ItemList.jsx       # Items display
│   ├── services/
│   │   └── api.js             # API communication
│   ├── utils/
│   │   └── dateUtils.js       # Date utilities
│   ├── App.jsx                # Main app component
│   └── index.css              # Tailwind CSS
└── package.json
```

## 🔌 API Endpoints

### Items Management
- `GET /api/items` - Get all items
- `GET /api/items/:id` - Get item by ID
- `POST /api/items` - Create new item
- `PUT /api/items/:id` - Update item
- `DELETE /api/items/:id` - Delete item

### Filtering
- `GET /api/items/filter/expiring-soon?days=7` - Get expiring items
- `GET /api/items/filter/expired` - Get expired items
- `GET /api/items/category/:category` - Get items by category

### Statistics
- `GET /api/items/stats/dashboard` - Get dashboard statistics

### Health Check
- `GET /api/health` - API health status

## 🗄️ Database Schema

### Items Table
```sql
CREATE TABLE items (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  item_name TEXT NOT NULL,
  category TEXT NOT NULL,
  expiration_date DATE NOT NULL,
  notes TEXT,
  quantity INTEGER DEFAULT 1,
  location TEXT,
  created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_date DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 🎨 Styling & UI

- **Framework**: Tailwind CSS
- **Icons**: Lucide React
- **Design**: Modern, clean, responsive
- **Color Scheme**: Professional blue with status-based colors
- **Mobile-First**: Responsive design for all screen sizes

## 🔧 Development

### Available Scripts

**Backend:**
- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm run seed` - Seed database with sample data

**Frontend:**
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build

### Adding New Features

1. **Backend**: Add routes in `routes/items.js` and models in `models/`
2. **Frontend**: Create components in `components/` and update API calls in `services/api.js`
3. **Database**: Modify schema in `database/db.js`

## 🚀 Deployment

### Production Build

1. **Build Frontend**
   ```bash
   cd frontend
   npm run build
   ```

2. **Configure Backend for Production**
   ```bash
   cd backend
   npm start
   ```

### Environment Variables
Create `.env` file in backend directory:
```env
PORT=3001
NODE_ENV=production
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📝 License

This project is licensed under the ISC License.

## 🆘 Support

If you encounter any issues:

1. Check that both servers are running
2. Verify database connection
3. Check browser console for errors
4. Ensure all dependencies are installed

### Common Issues

**Backend won't start:**
- Check if port 3001 is available
- Verify Node.js version (18+)
- Run `npm install` in backend directory

**Frontend won't start:**
- Check if port 5173 is available
- Verify Node.js version (18+)
- Run `npm install` in frontend directory

**Database issues:**
- Delete `backend/database/pharmacy.db` and restart
- Run `npm run seed` to add sample data

## 🎉 Acknowledgments

- Built with React, Node.js, Express, and SQLite
- UI components styled with Tailwind CSS
- Icons provided by Lucide React
- Date utilities powered by date-fns

---

**Happy Inventory Management! 💊📦**
