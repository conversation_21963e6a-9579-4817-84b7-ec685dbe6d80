{"version": 3, "names": ["unwrapFetchResult", "response", "data", "text", "JSON", "parse", "e", "fetchToTemp", "url", "Promise", "resolve", "reject", "fileName", "path", "basename", "tmpDir", "join", "os", "tmpdir", "global", "fetch", "then", "result", "status", "body", "dest", "fs", "createWriteStream", "stream", "Readable", "fromWeb", "pipe", "on", "logger", "error", "options", "CLIError", "headers"], "sources": ["../src/fetch.ts"], "sourcesContent": ["import * as os from 'os';\nimport * as path from 'path';\nimport * as fs from 'fs';\nimport * as stream from 'stream';\n\nimport {CLIError} from './errors';\nimport logger from './logger';\n\nasync function unwrapFetchResult(response: Response) {\n  const data = await response.text();\n\n  try {\n    return JSON.parse(data);\n  } catch (e) {\n    return data;\n  }\n}\n\n/**\n * Downloads the given `url` to the OS's temp folder and\n * returns the path to it.\n */\nconst fetchToTemp = (url: string): Promise<string> => {\n  try {\n    return new Promise((resolve, reject) => {\n      const fileName = path.basename(url);\n      const tmpDir = path.join(os.tmpdir(), fileName);\n\n      global.fetch(url).then((result) => {\n        if (result.status >= 400) {\n          return reject(`Fetch request failed with status ${result.status}`);\n        }\n\n        if (result.body === null) {\n          return reject('Fetch request failed - empty body');\n        }\n\n        const dest = fs.createWriteStream(tmpDir);\n        const body = stream.Readable.fromWeb(result.body);\n\n        body.pipe(dest);\n\n        body.on('end', () => {\n          resolve(tmpDir);\n        });\n\n        body.on('error', reject);\n      });\n    });\n  } catch (e) {\n    logger.error(e as any);\n    throw e;\n  }\n};\n\nconst fetch = async (\n  url: string | Request,\n  options?: RequestInit,\n): Promise<{status: number; data: any; headers: Headers}> => {\n  const result = await global.fetch(url, options);\n  const data = await unwrapFetchResult(result);\n\n  if (result.status >= 400) {\n    throw new CLIError(\n      `Fetch request failed with status ${result.status}: ${data}.`,\n    );\n  }\n\n  return {\n    status: result.status,\n    headers: result.headers,\n    data,\n  };\n};\n\nexport {fetch, fetchToTemp};\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AACA;AAA8B;AAAA;AAAA;AAE9B,eAAeA,iBAAiB,CAACC,QAAkB,EAAE;EACnD,MAAMC,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAI,EAAE;EAElC,IAAI;IACF,OAAOC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC;EACzB,CAAC,CAAC,OAAOI,CAAC,EAAE;IACV,OAAOJ,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA,MAAMK,WAAW,GAAIC,GAAW,IAAsB;EACpD,IAAI;IACF,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,MAAMC,QAAQ,GAAGC,IAAI,GAACC,QAAQ,CAACN,GAAG,CAAC;MACnC,MAAMO,MAAM,GAAGF,IAAI,GAACG,IAAI,CAACC,EAAE,GAACC,MAAM,EAAE,EAAEN,QAAQ,CAAC;MAE/CO,MAAM,CAACC,KAAK,CAACZ,GAAG,CAAC,CAACa,IAAI,CAAEC,MAAM,IAAK;QACjC,IAAIA,MAAM,CAACC,MAAM,IAAI,GAAG,EAAE;UACxB,OAAOZ,MAAM,CAAE,oCAAmCW,MAAM,CAACC,MAAO,EAAC,CAAC;QACpE;QAEA,IAAID,MAAM,CAACE,IAAI,KAAK,IAAI,EAAE;UACxB,OAAOb,MAAM,CAAC,mCAAmC,CAAC;QACpD;QAEA,MAAMc,IAAI,GAAGC,EAAE,GAACC,iBAAiB,CAACZ,MAAM,CAAC;QACzC,MAAMS,IAAI,GAAGI,MAAM,GAACC,QAAQ,CAACC,OAAO,CAACR,MAAM,CAACE,IAAI,CAAC;QAEjDA,IAAI,CAACO,IAAI,CAACN,IAAI,CAAC;QAEfD,IAAI,CAACQ,EAAE,CAAC,KAAK,EAAE,MAAM;UACnBtB,OAAO,CAACK,MAAM,CAAC;QACjB,CAAC,CAAC;QAEFS,IAAI,CAACQ,EAAE,CAAC,OAAO,EAAErB,MAAM,CAAC;MAC1B,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOL,CAAC,EAAE;IACV2B,eAAM,CAACC,KAAK,CAAC5B,CAAC,CAAQ;IACtB,MAAMA,CAAC;EACT;AACF,CAAC;AAAC;AAEF,MAAMc,KAAK,GAAG,OACZZ,GAAqB,EACrB2B,OAAqB,KACsC;EAC3D,MAAMb,MAAM,GAAG,MAAMH,MAAM,CAACC,KAAK,CAACZ,GAAG,EAAE2B,OAAO,CAAC;EAC/C,MAAMjC,IAAI,GAAG,MAAMF,iBAAiB,CAACsB,MAAM,CAAC;EAE5C,IAAIA,MAAM,CAACC,MAAM,IAAI,GAAG,EAAE;IACxB,MAAM,IAAIa,gBAAQ,CACf,oCAAmCd,MAAM,CAACC,MAAO,KAAIrB,IAAK,GAAE,CAC9D;EACH;EAEA,OAAO;IACLqB,MAAM,EAAED,MAAM,CAACC,MAAM;IACrBc,OAAO,EAAEf,MAAM,CAACe,OAAO;IACvBnC;EACF,CAAC;AACH,CAAC;AAAC"}