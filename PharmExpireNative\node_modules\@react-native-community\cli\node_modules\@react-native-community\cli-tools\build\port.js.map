{"version": 3, "names": ["askForPortChange", "port", "nextPort", "logger", "info", "prompt", "name", "type", "message", "choices", "title", "value", "logAlreadyRunningBundler", "logChangePortInstructions"], "sources": ["../src/port.ts"], "sourcesContent": ["import {prompt} from './prompt';\nimport logger from './logger';\n\nexport const askForPortChange = async (port: number, nextPort: number) => {\n  logger.info(`Another process is running on port ${port}.`);\n  return await prompt({\n    name: 'change',\n    type: 'select',\n    message: `Use port ${nextPort} instead?`,\n    choices: [\n      {title: 'Yes', value: true},\n      {title: 'No', value: false},\n    ],\n  });\n};\n\nexport const logAlreadyRunningBundler = (port: number) => {\n  logger.info(\n    `A dev server is already running for this project on port ${port}.`,\n  );\n};\n\nexport const logChangePortInstructions = () => {\n  logger.info(\n    'Please terminate this process and try again, or use another port with \"--port\".',\n  );\n};\n"], "mappings": ";;;;;;AAAA;AACA;AAA8B;AAEvB,MAAMA,gBAAgB,GAAG,OAAOC,IAAY,EAAEC,QAAgB,KAAK;EACxEC,eAAM,CAACC,IAAI,CAAE,sCAAqCH,IAAK,GAAE,CAAC;EAC1D,OAAO,MAAM,IAAAI,cAAM,EAAC;IAClBC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAG,YAAWN,QAAS,WAAU;IACxCO,OAAO,EAAE,CACP;MAACC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAI,CAAC,EAC3B;MAACD,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC;EAE/B,CAAC,CAAC;AACJ,CAAC;AAAC;AAEK,MAAMC,wBAAwB,GAAIX,IAAY,IAAK;EACxDE,eAAM,CAACC,IAAI,CACR,4DAA2DH,IAAK,GAAE,CACpE;AACH,CAAC;AAAC;AAEK,MAAMY,yBAAyB,GAAG,MAAM;EAC7CV,eAAM,CAACC,IAAI,CACT,iFAAiF,CAClF;AACH,CAAC;AAAC"}