-- Merging decision tree log ---
manifest
ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:1:1-26:12
MERGED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:1:1-26:12
INJECTED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-gesture-handler] D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-vector-icons] D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.80.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d221509c521a46e74f6572064742339\transformed\react-android-0.80.1-debug\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3e7df312ad5b5ee0d6592d754d6c3af1\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5bca0cfaae241dea23eba63d623ea919\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\239bab1936ed930a97e31eb44b335c5c\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\625ed137c6a3f5343b71917adedb437c\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fa18728a7be038b69ee6e21ef63b1c06\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1adfcbfd69b825f3e5c1f306c67466e9\transformed\fragment-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3fe47c7edcfaa728488b2ae1a70ee680\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cabd6b3dfaf0b5849e6d9bed54bfd3e2\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bfef782026e93cfc945e58eb2e9dfb5e\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3cb7bf04a84099f6bc24510b14a8d32\transformed\autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6f803d8da21c4dc075759336e7569e71\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\728a39e1b0ee21524c7671cbb76c52db\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1eac0c58faa3f9317570b0c5d69f3c27\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b969c2a1c5e324d0a2d2345bfcefc7a0\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc8498a9657a6f40ee5be824770bb565\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ded77998d6991a74a694fd63b4af3c2a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f754c710f4479097018481dcee972409\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\75f30127dc54fec758f4aa7347dd0654\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5d3738e50d488dad5737cb7b74d6eee7\transformed\fresco-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\00b03a8634c2252121ce18467cd792bc\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\956159063502be591b57ff4f5aaf7fef\transformed\drawee-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ea21ba60f5ca5e3b6210a51d223864a5\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0683b07cae0070ba68ff9dde96117a8d\transformed\memory-type-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6f217aceb2b4a0e81a7da8af6300133a\transformed\memory-type-java-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a8fa3656c0c691a623e836ced0654fbc\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5441e925cbbaab8558033fba83f56098\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b381a187a8ac91ce3a8025ac0d6bf59\transformed\imagepipeline-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\efdea0ab8419de6825226b7e4412a067\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4b0f30d4033881bf2d5cc4fa54e2172f\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d0765f5d48b93ec5a97559fdf7a2b17\transformed\urimod-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\27a46c0c30537e82f0dcff3c23499704\transformed\vito-source-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3447548837983fe444c234e94e57b7f4\transformed\middleware-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e35f83bd7e41599aceaf0d4ce5d0c0c8\transformed\ui-common-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\da71b07372af9691115e11798ef05622\transformed\soloader-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c5b879471b4556be91eedbd9dab05d36\transformed\fbcore-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2045a995f84570414759fae539138f81\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\db52d1003d1ae1c20ba2e3486e74ea7c\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2af4298c48b6932450e120370af731ca\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\761fe3c87fdfe119aaff47b812dc118b\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59097513b936cb008503d7cbe4325132\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2a3e8a381e7e40f9a35176b00f579d2d\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\875a7da5ac63e51a49a89062b608b501\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cab5a80bf7fe1dfbbd5713f96028e59f\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1e8ac3c34f4ad66978c651e59c9ade22\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fc94c51d851743903799ffe3e8cb2c44\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f7cdf5cb9559c5edcdf8f836411df92\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\22777817996210683c00c27568dea84f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6ce5005bcde927970eb9eb565e879031\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dd91f2fa8a18639f14a2c2b25180debc\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17365bb8d44a96e8cb68a585945c3ff1\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\824d221d5c56526c3c6ddbae0b799560\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ba2bfc587819729513a24a289a58aaaf\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cdbc2fc941170b6004e5d876c052f435\transformed\ui-core-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bf3dbfc63c3eda7a1c918d0d25cfb4e6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5db4160556851438fa19525aa3f72de9\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3919d8aa3fd4eaa4ecf82c67c0b64d5e\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f8caf368debc3686ade571a4ec56cfe6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68c71bb8eb87df5d47628b16ad903f58\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\73633f5366d7e42eb33ae1b58ff38e87\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cfda843d221c7111e1c82fff2513522e\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1f08a6dfbb3f0886978757fc99843d08\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f400e7674646183cc40a1e8047804f18\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4bc0aeaf811ea1d98e6f3bebf6c35d88\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\204d19d4581a6130de28810e01d2d447\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\01b3cc6ba7b89a751b7226c9f68a2db0\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.react-native-community:jsc-android:2026004.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eec467ac002a7e5675791274b61bb229\transformed\jsc-android-2026004.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b6a6ccfe82730aba80016e7d9940f54a\transformed\fbjni-0.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\363f04887ef37f5443484b40dba2058e\transformed\soloader-0.12.1\AndroidManifest.xml:2:1-17:12
	package
		INJECTED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\debug\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:3:5-67
	android:name
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:3:22-64
application
ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:5:5-25:19
MERGED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:5:5-25:19
MERGED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:5:5-25:19
INJECTED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\debug\AndroidManifest.xml:5:5-8:50
MERGED from [com.facebook.react:react-android:0.80.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d221509c521a46e74f6572064742339\transformed\react-android-0.80.1-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.80.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d221509c521a46e74f6572064742339\transformed\react-android-0.80.1-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3e7df312ad5b5ee0d6592d754d6c3af1\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3e7df312ad5b5ee0d6592d754d6c3af1\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\239bab1936ed930a97e31eb44b335c5c\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\239bab1936ed930a97e31eb44b335c5c\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\22777817996210683c00c27568dea84f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\22777817996210683c00c27568dea84f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f8caf368debc3686ade571a4ec56cfe6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f8caf368debc3686ade571a4ec56cfe6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\73633f5366d7e42eb33ae1b58ff38e87\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\73633f5366d7e42eb33ae1b58ff38e87\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\363f04887ef37f5443484b40dba2058e\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\363f04887ef37f5443484b40dba2058e\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
	android:extractNativeLibs
		INJECTED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:12:7-33
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:12:7-33
	android:label
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:7:7-39
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:7:7-39
	tools:ignore
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\debug\AndroidManifest.xml:8:9-48
	android:roundIcon
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:9:7-52
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:9:7-52
	tools:targetApi
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\debug\AndroidManifest.xml:7:9-29
	android:icon
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:8:7-41
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:8:7-41
	android:allowBackup
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:10:7-34
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:10:7-34
	android:theme
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:11:7-38
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:11:7-38
	android:usesCleartextTraffic
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\debug\AndroidManifest.xml:6:9-44
	android:name
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:6:7-38
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:6:7-38
activity#com.pharmexpirenative.MainActivity
ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:13:7-24:18
	android:label
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:15:9-41
	android:launchMode
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:17:9-40
	android:windowSoftInputMode
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:18:9-51
	android:exported
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:19:9-32
	android:configChanges
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:16:9-118
	android:name
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:14:9-37
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:20:9-23:25
action#android.intent.action.MAIN
ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:21:13-65
	android:name
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:21:21-62
category#android.intent.category.LAUNCHER
ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:22:13-73
	android:name
		ADDED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:22:23-70
uses-sdk
INJECTED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\debug\AndroidManifest.xml
MERGED from [:react-native-gesture-handler] D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.80.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d221509c521a46e74f6572064742339\transformed\react-android-0.80.1-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.80.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d221509c521a46e74f6572064742339\transformed\react-android-0.80.1-debug\AndroidManifest.xml:10:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3e7df312ad5b5ee0d6592d754d6c3af1\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3e7df312ad5b5ee0d6592d754d6c3af1\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5bca0cfaae241dea23eba63d623ea919\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5bca0cfaae241dea23eba63d623ea919\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\239bab1936ed930a97e31eb44b335c5c\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\239bab1936ed930a97e31eb44b335c5c\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\625ed137c6a3f5343b71917adedb437c\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\625ed137c6a3f5343b71917adedb437c\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fa18728a7be038b69ee6e21ef63b1c06\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fa18728a7be038b69ee6e21ef63b1c06\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1adfcbfd69b825f3e5c1f306c67466e9\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1adfcbfd69b825f3e5c1f306c67466e9\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3fe47c7edcfaa728488b2ae1a70ee680\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3fe47c7edcfaa728488b2ae1a70ee680\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cabd6b3dfaf0b5849e6d9bed54bfd3e2\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cabd6b3dfaf0b5849e6d9bed54bfd3e2\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bfef782026e93cfc945e58eb2e9dfb5e\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bfef782026e93cfc945e58eb2e9dfb5e\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3cb7bf04a84099f6bc24510b14a8d32\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a3cb7bf04a84099f6bc24510b14a8d32\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6f803d8da21c4dc075759336e7569e71\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6f803d8da21c4dc075759336e7569e71\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\728a39e1b0ee21524c7671cbb76c52db\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\728a39e1b0ee21524c7671cbb76c52db\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1eac0c58faa3f9317570b0c5d69f3c27\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1eac0c58faa3f9317570b0c5d69f3c27\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b969c2a1c5e324d0a2d2345bfcefc7a0\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b969c2a1c5e324d0a2d2345bfcefc7a0\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc8498a9657a6f40ee5be824770bb565\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dc8498a9657a6f40ee5be824770bb565\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ded77998d6991a74a694fd63b4af3c2a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ded77998d6991a74a694fd63b4af3c2a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f754c710f4479097018481dcee972409\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f754c710f4479097018481dcee972409\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\75f30127dc54fec758f4aa7347dd0654\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\75f30127dc54fec758f4aa7347dd0654\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5d3738e50d488dad5737cb7b74d6eee7\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5d3738e50d488dad5737cb7b74d6eee7\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\00b03a8634c2252121ce18467cd792bc\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\00b03a8634c2252121ce18467cd792bc\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\956159063502be591b57ff4f5aaf7fef\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\956159063502be591b57ff4f5aaf7fef\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ea21ba60f5ca5e3b6210a51d223864a5\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ea21ba60f5ca5e3b6210a51d223864a5\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0683b07cae0070ba68ff9dde96117a8d\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0683b07cae0070ba68ff9dde96117a8d\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6f217aceb2b4a0e81a7da8af6300133a\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6f217aceb2b4a0e81a7da8af6300133a\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a8fa3656c0c691a623e836ced0654fbc\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a8fa3656c0c691a623e836ced0654fbc\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5441e925cbbaab8558033fba83f56098\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5441e925cbbaab8558033fba83f56098\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b381a187a8ac91ce3a8025ac0d6bf59\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b381a187a8ac91ce3a8025ac0d6bf59\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\efdea0ab8419de6825226b7e4412a067\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\efdea0ab8419de6825226b7e4412a067\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4b0f30d4033881bf2d5cc4fa54e2172f\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4b0f30d4033881bf2d5cc4fa54e2172f\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d0765f5d48b93ec5a97559fdf7a2b17\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d0765f5d48b93ec5a97559fdf7a2b17\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\27a46c0c30537e82f0dcff3c23499704\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\27a46c0c30537e82f0dcff3c23499704\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3447548837983fe444c234e94e57b7f4\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3447548837983fe444c234e94e57b7f4\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e35f83bd7e41599aceaf0d4ce5d0c0c8\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e35f83bd7e41599aceaf0d4ce5d0c0c8\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\da71b07372af9691115e11798ef05622\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\da71b07372af9691115e11798ef05622\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c5b879471b4556be91eedbd9dab05d36\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c5b879471b4556be91eedbd9dab05d36\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2045a995f84570414759fae539138f81\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2045a995f84570414759fae539138f81\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\db52d1003d1ae1c20ba2e3486e74ea7c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\db52d1003d1ae1c20ba2e3486e74ea7c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2af4298c48b6932450e120370af731ca\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2af4298c48b6932450e120370af731ca\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\761fe3c87fdfe119aaff47b812dc118b\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\761fe3c87fdfe119aaff47b812dc118b\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59097513b936cb008503d7cbe4325132\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59097513b936cb008503d7cbe4325132\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2a3e8a381e7e40f9a35176b00f579d2d\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2a3e8a381e7e40f9a35176b00f579d2d\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\875a7da5ac63e51a49a89062b608b501\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\875a7da5ac63e51a49a89062b608b501\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cab5a80bf7fe1dfbbd5713f96028e59f\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cab5a80bf7fe1dfbbd5713f96028e59f\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1e8ac3c34f4ad66978c651e59c9ade22\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1e8ac3c34f4ad66978c651e59c9ade22\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fc94c51d851743903799ffe3e8cb2c44\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fc94c51d851743903799ffe3e8cb2c44\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f7cdf5cb9559c5edcdf8f836411df92\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f7cdf5cb9559c5edcdf8f836411df92\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\22777817996210683c00c27568dea84f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\22777817996210683c00c27568dea84f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6ce5005bcde927970eb9eb565e879031\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6ce5005bcde927970eb9eb565e879031\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dd91f2fa8a18639f14a2c2b25180debc\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dd91f2fa8a18639f14a2c2b25180debc\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17365bb8d44a96e8cb68a585945c3ff1\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17365bb8d44a96e8cb68a585945c3ff1\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\824d221d5c56526c3c6ddbae0b799560\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\824d221d5c56526c3c6ddbae0b799560\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ba2bfc587819729513a24a289a58aaaf\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ba2bfc587819729513a24a289a58aaaf\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cdbc2fc941170b6004e5d876c052f435\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cdbc2fc941170b6004e5d876c052f435\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bf3dbfc63c3eda7a1c918d0d25cfb4e6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bf3dbfc63c3eda7a1c918d0d25cfb4e6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5db4160556851438fa19525aa3f72de9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5db4160556851438fa19525aa3f72de9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3919d8aa3fd4eaa4ecf82c67c0b64d5e\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3919d8aa3fd4eaa4ecf82c67c0b64d5e\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f8caf368debc3686ade571a4ec56cfe6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f8caf368debc3686ade571a4ec56cfe6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68c71bb8eb87df5d47628b16ad903f58\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\68c71bb8eb87df5d47628b16ad903f58\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\73633f5366d7e42eb33ae1b58ff38e87\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\73633f5366d7e42eb33ae1b58ff38e87\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cfda843d221c7111e1c82fff2513522e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cfda843d221c7111e1c82fff2513522e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1f08a6dfbb3f0886978757fc99843d08\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1f08a6dfbb3f0886978757fc99843d08\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f400e7674646183cc40a1e8047804f18\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f400e7674646183cc40a1e8047804f18\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4bc0aeaf811ea1d98e6f3bebf6c35d88\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4bc0aeaf811ea1d98e6f3bebf6c35d88\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\204d19d4581a6130de28810e01d2d447\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\204d19d4581a6130de28810e01d2d447\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\01b3cc6ba7b89a751b7226c9f68a2db0\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\01b3cc6ba7b89a751b7226c9f68a2db0\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.github.react-native-community:jsc-android:2026004.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eec467ac002a7e5675791274b61bb229\transformed\jsc-android-2026004.0.1\AndroidManifest.xml:5:5-44
MERGED from [io.github.react-native-community:jsc-android:2026004.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eec467ac002a7e5675791274b61bb229\transformed\jsc-android-2026004.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b6a6ccfe82730aba80016e7d9940f54a\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b6a6ccfe82730aba80016e7d9940f54a\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\363f04887ef37f5443484b40dba2058e\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\363f04887ef37f5443484b40dba2058e\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\debug\AndroidManifest.xml
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from [com.facebook.react:react-android:0.80.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d221509c521a46e74f6572064742339\transformed\react-android-0.80.1-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from [com.facebook.react:react-android:0.80.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d221509c521a46e74f6572064742339\transformed\react-android-0.80.1-debug\AndroidManifest.xml:16:22-75
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.80.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d221509c521a46e74f6572064742339\transformed\react-android-0.80.1-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.80.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d221509c521a46e74f6572064742339\transformed\react-android-0.80.1-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.80.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d221509c521a46e74f6572064742339\transformed\react-android-0.80.1-debug\AndroidManifest.xml:20:13-77
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\22777817996210683c00c27568dea84f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\22777817996210683c00c27568dea84f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f8caf368debc3686ade571a4ec56cfe6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f8caf368debc3686ade571a4ec56cfe6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.pharmexpirenative.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.pharmexpirenative.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\22777817996210683c00c27568dea84f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\22777817996210683c00c27568dea84f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\22777817996210683c00c27568dea84f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
meta-data#com.facebook.soloader.enabled
ADDED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\363f04887ef37f5443484b40dba2058e\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	android:value
		ADDED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\363f04887ef37f5443484b40dba2058e\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\363f04887ef37f5443484b40dba2058e\transformed\soloader-0.12.1\AndroidManifest.xml:13:13-57
