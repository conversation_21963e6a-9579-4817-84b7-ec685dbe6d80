                        -HD:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.1.12297006
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.1.12297006
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.1.12297006\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\cxx\Debug\2e4v5m5x\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\cxx\Debug\2e4v5m5x\obj\x86
-DCMAKE_BUILD_TYPE=Debug
-DCMAKE_FIND_ROOT_PATH=D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\.cxx\Debug\2e4v5m5x\prefab\x86\prefab
-BD:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\.cxx\Debug\2e4v5m5x\x86
-GNinja
-DPROJECT_BUILD_DIR=D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build
-DPROJECT_ROOT_DIR=D:\copy from kpi tracker app\expire management\PharmExpireNative\android
-DREACT_ANDROID_DIR=D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native\ReactAndroid
-DANDROID_STL=c++_shared
-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON
                        Build command args: []
                        Version: 2