{"version": 3, "names": ["_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "React", "isSearchBarAvailableForCurrentPlatform", "View", "SearchBarNativeComponent", "Commands", "SearchBarNativeCommands", "NativeSearchBar", "NativeSearchBarCommands", "SearchBar", "props", "forwardedRef", "searchBarRef", "useRef", "useImperativeHandle", "blur", "_callMethodWithRef", "ref", "focus", "toggleCancelButton", "flag", "clearText", "setText", "text", "cancelSearch", "useCallback", "method", "current", "console", "warn", "createElement", "onSearchFocus", "onFocus", "onSearchBlur", "onBlur", "onSearchButtonPress", "onCancelButtonPress", "onChangeText", "forwardRef"], "sourceRoot": "../../../src", "sources": ["components/SearchBar.tsx"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,SAAA,WAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,CAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAL,CAAA,CAAAK,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAL,CAAA,KAAAJ,QAAA,CAAAY,KAAA,OAAAN,SAAA;AAEb,OAAOO,KAAK,MAAM,OAAO;AAEzB,SAASC,sCAAsC,QAAQ,UAAU;AACjE,SAASC,IAAI,QAAQ,cAAc;;AAEnC;AACA,OAAOC,wBAAwB,IAC7BC,QAAQ,IAAIC,uBAAuB,QAK9B,oCAAoC;AAG3C,MAAMC,eAG0B,GAC9BH,wBACuB;AACzB,MAAMI,uBAA8C,GAClDF,uBAAgD;AAalD,SAASG,SAASA,CAChBC,KAAqB,EACrBC,YAA0C,EAC1C;EACA,MAAMC,YAAY,GAAGX,KAAK,CAACY,MAAM,CAA2B,IAAI,CAAC;EAEjEZ,KAAK,CAACa,mBAAmB,CAACH,YAAY,EAAE,OAAO;IAC7CI,IAAI,EAAEA,CAAA,KAAM;MACVC,kBAAkB,CAACC,GAAG,IAAIT,uBAAuB,CAACO,IAAI,CAACE,GAAG,CAAC,CAAC;IAC9D,CAAC;IACDC,KAAK,EAAEA,CAAA,KAAM;MACXF,kBAAkB,CAACC,GAAG,IAAIT,uBAAuB,CAACU,KAAK,CAACD,GAAG,CAAC,CAAC;IAC/D,CAAC;IACDE,kBAAkB,EAAGC,IAAa,IAAK;MACrCJ,kBAAkB,CAACC,GAAG,IACpBT,uBAAuB,CAACW,kBAAkB,CAACF,GAAG,EAAEG,IAAI,CACtD,CAAC;IACH,CAAC;IACDC,SAAS,EAAEA,CAAA,KAAM;MACfL,kBAAkB,CAACC,GAAG,IAAIT,uBAAuB,CAACa,SAAS,CAACJ,GAAG,CAAC,CAAC;IACnE,CAAC;IACDK,OAAO,EAAGC,IAAY,IAAK;MACzBP,kBAAkB,CAACC,GAAG,IAAIT,uBAAuB,CAACc,OAAO,CAACL,GAAG,EAAEM,IAAI,CAAC,CAAC;IACvE,CAAC;IACDC,YAAY,EAAEA,CAAA,KAAM;MAClBR,kBAAkB,CAACC,GAAG,IAAIT,uBAAuB,CAACgB,YAAY,CAACP,GAAG,CAAC,CAAC;IACtE;EACF,CAAC,CAAC,CAAC;EAEH,MAAMD,kBAAkB,GAAGf,KAAK,CAACwB,WAAW,CACzCC,MAAwC,IAAK;IAC5C,MAAMT,GAAG,GAAGL,YAAY,CAACe,OAAO;IAChC,IAAIV,GAAG,EAAE;MACPS,MAAM,CAACT,GAAG,CAAC;IACb,CAAC,MAAM;MACLW,OAAO,CAACC,IAAI,CACV,mEACF,CAAC;IACH;EACF,CAAC,EACD,CAACjB,YAAY,CACf,CAAC;EAED,IAAI,CAACV,sCAAsC,EAAE;IAC3C0B,OAAO,CAACC,IAAI,CACV,+DACF,CAAC;IACD,OAAO1B,IAAI;EACb;EAEA,oBACEF,KAAA,CAAA6B,aAAA,CAACvB,eAAe,EAAAnB,QAAA;IACd6B,GAAG,EAAEL;EAAa,GACdF,KAAK;IACTqB,aAAa,EAAErB,KAAK,CAACsB,OAA8C;IACnEC,YAAY,EAAEvB,KAAK,CAACwB,MAA6C;IACjEC,mBAAmB,EACjBzB,KAAK,CAACyB,mBACP;IACDC,mBAAmB,EACjB1B,KAAK,CAAC0B,mBACP;IACDC,YAAY,EAAE3B,KAAK,CAAC2B;EAAoD,EACzE,CAAC;AAEN;AAEA,4BAAepC,KAAK,CAACqC,UAAU,CAAoC7B,SAAS,CAAC", "ignoreList": []}