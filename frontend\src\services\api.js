import axios from 'axios';

const API_BASE_URL = 'http://localhost:3001/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Items API
export const itemsAPI = {
  // Get all items
  getAll: () => api.get('/items'),
  
  // Get item by ID
  getById: (id) => api.get(`/items/${id}`),
  
  // Get items expiring soon
  getExpiringSoon: (days = 7) => api.get(`/items/filter/expiring-soon?days=${days}`),
  
  // Get expired items
  getExpired: () => api.get('/items/filter/expired'),
  
  // Get items by category
  getByCategory: (category) => api.get(`/items/category/${category}`),
  
  // Get dashboard statistics
  getStats: () => api.get('/items/stats/dashboard'),
  
  // Create new item
  create: (itemData) => api.post('/items', itemData),
  
  // Update item
  update: (id, itemData) => api.put(`/items/${id}`, itemData),
  
  // Delete item
  delete: (id) => api.delete(`/items/${id}`),
};

// Health check
export const healthCheck = () => api.get('/health');

export default api;
