{"artifacts": [{"path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/build/intermediates/cxx/Debug/2e4v5m5x/obj/x86/libappmodules.so"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "target_compile_options", "target_compile_reactnative_options", "target_include_directories"], "files": ["D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake", "CMakeLists.txt", "D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native/ReactCommon/cmake-utils/react-native-flags.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 31, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 64, "parent": 2}, {"command": 2, "file": 0, "line": 81, "parent": 2}, {"command": 2, "file": 0, "line": 95, "parent": 2}, {"command": 4, "file": 0, "line": 71, "parent": 2}, {"command": 3, "file": 2, "line": 30, "parent": 6}, {"command": 3, "file": 2, "line": 36, "parent": 6}, {"command": 5, "file": 0, "line": 66, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}, {"backtrace": 7, "fragment": "-Wall"}, {"backtrace": 7, "fragment": "-Werror"}, {"backtrace": 7, "fragment": "-fexceptions"}, {"backtrace": 7, "fragment": "-frtti"}, {"backtrace": 7, "fragment": "-std=c++20"}, {"backtrace": 7, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 8, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 5, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 5, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 5, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 5, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 5, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 5, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 5, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "defines": [{"define": "appmodules_EXPORTS"}], "includes": [{"backtrace": 9, "path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, {"backtrace": 9, "path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/build/generated/autolinking/src/main/jni"}, {"backtrace": 5, "path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/."}, {"backtrace": 5, "path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen"}, {"backtrace": 5, "path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/."}, {"backtrace": 5, "path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec"}, {"backtrace": 4, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 4, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include"}, {"backtrace": 4, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"}], "language": "CXX", "sourceIndexes": [0, 1], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "dependencies": [{"backtrace": 5, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d"}, {"backtrace": 5, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec"}], "id": "appmodules::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 4, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b6a6ccfe82730aba80016e7d9940f54a\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "role": "libraries"}, {"backtrace": 4, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "role": "libraries"}, {"backtrace": 4, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "appmodules", "nameOnDisk": "libappmodules.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}, {"name": "Object Libraries", "sourceIndexes": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "OnLoad.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}