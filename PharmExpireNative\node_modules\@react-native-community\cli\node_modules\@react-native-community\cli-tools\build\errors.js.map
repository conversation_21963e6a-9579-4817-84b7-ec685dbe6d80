{"version": 3, "names": ["CLIError", "Error", "constructor", "msg", "originalError", "inlineString", "stack", "split", "slice", "join", "UnknownProjectError", "str", "replace", "trim"], "sources": ["../src/errors.ts"], "sourcesContent": ["/**\n * A custom Error that creates a single-lined message to match current styling inside CLI.\n * Uses original stack trace when `originalError` is passed or erase the stack if it's not defined.\n */\nexport class CLIError extends Error {\n  constructor(msg: string, originalError?: Error | string) {\n    super(inlineString(msg));\n    if (originalError) {\n      this.stack =\n        typeof originalError === 'string'\n          ? originalError\n          : originalError.stack || ''.split('\\n').slice(0, 2).join('\\n');\n    } else {\n      // When the \"originalError\" is not passed, it means that we know exactly\n      // what went wrong and provide means to fix it. In such cases showing the\n      // stack is an unnecessary clutter to the CLI output, hence removing it.\n      delete this.stack;\n    }\n  }\n}\n\n/**\n * Raised when we're unable to find a package.json\n */\nexport class UnknownProjectError extends Error {}\n\nexport const inlineString = (str: string = '') =>\n  str.replace(/(\\s{2,})/gm, ' ').trim();\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACO,MAAMA,QAAQ,SAASC,KAAK,CAAC;EAClCC,WAAW,CAACC,GAAW,EAAEC,aAA8B,EAAE;IACvD,KAAK,CAACC,YAAY,CAACF,GAAG,CAAC,CAAC;IACxB,IAAIC,aAAa,EAAE;MACjB,IAAI,CAACE,KAAK,GACR,OAAOF,aAAa,KAAK,QAAQ,GAC7BA,aAAa,GACbA,aAAa,CAACE,KAAK,IAAI,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IACpE,CAAC,MAAM;MACL;MACA;MACA;MACA,OAAO,IAAI,CAACH,KAAK;IACnB;EACF;AACF;;AAEA;AACA;AACA;AAFA;AAGO,MAAMI,mBAAmB,SAAST,KAAK,CAAC;AAAE;AAE1C,MAAMI,YAAY,GAAG,CAACM,GAAW,GAAG,EAAE,KAC3CA,GAAG,CAACC,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC,CAACC,IAAI,EAAE;AAAC"}