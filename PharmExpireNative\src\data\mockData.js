/**
 * بيانات وهمية للتطبيق
 * Mock data for the app
 */

// بيانات وهمية للمنتجات
export const mockItems = [
  {
    id: 1,
    item_name: 'باراسيتامول 500mg',
    category: 'medicine',
    quantity: 50,
    expiration_date: '2026-08-15', // آمن
    batch_number: 'B001',
    supplier: 'شركة الأدوية المصرية',
    created_at: '2024-01-15T10:00:00Z'
  },
  {
    id: 2,
    item_name: 'فيتامين د 1000 وحدة',
    category: 'nutraceutical',
    quantity: 30,
    expiration_date: '2026-12-20', // آمن
    batch_number: 'V002',
    supplier: 'شركة الفيتامينات',
    created_at: '2024-02-10T14:30:00Z'
  },
  {
    id: 3,
    item_name: 'كريم مرطب للأطفال',
    category: 'mom&baby',
    quantity: 25,
    expiration_date: '2025-07-22', // ينتهي قريباً (خلال 7 أيام)
    batch_number: 'C003',
    supplier: 'شركة منتجات الأطفال',
    created_at: '2024-03-05T09:15:00Z'
  },
  {
    id: 4,
    item_name: 'كريم مضاد للتجاعيد',
    category: 'beauty',
    quantity: 40,
    expiration_date: '2026-01-10', // آمن
    batch_number: 'S004',
    supplier: 'شركة مستحضرات التجميل',
    created_at: '2024-04-12T16:45:00Z'
  },
  {
    id: 5,
    item_name: 'شامبو للشعر الجاف',
    category: 'personal_care',
    quantity: 15,
    expiration_date: '2025-07-20', // ينتهي قريباً (خلال 5 أيام)
    batch_number: 'H005',
    supplier: 'شركة العناية الشخصية',
    created_at: '2024-05-20T11:20:00Z'
  },
  {
    id: 6,
    item_name: 'أسبرين 100mg',
    category: 'medicine',
    quantity: 100,
    expiration_date: '2025-07-10', // منتهي الصلاحية (منذ 5 أيام)
    batch_number: 'A006',
    supplier: 'شركة الأدوية المصرية',
    created_at: '2024-01-25T08:30:00Z'
  },
  {
    id: 7,
    item_name: 'حليب أطفال',
    category: 'mom&baby',
    quantity: 20,
    expiration_date: '2026-09-15', // آمن
    batch_number: 'M007',
    supplier: 'شركة منتجات الأطفال',
    created_at: '2024-06-01T12:00:00Z'
  },
  {
    id: 8,
    item_name: 'كريم واقي الشمس',
    category: 'beauty',
    quantity: 35,
    expiration_date: '2025-07-18', // ينتهي قريباً (خلال 3 أيام)
    batch_number: 'S008',
    supplier: 'شركة مستحضرات التجميل',
    created_at: '2024-06-15T14:30:00Z'
  },
  {
    id: 9,
    item_name: 'معجون أسنان',
    category: 'personal_care',
    quantity: 45,
    expiration_date: '2026-03-20', // آمن
    batch_number: 'T009',
    supplier: 'شركة العناية الشخصية',
    created_at: '2024-07-01T09:45:00Z'
  },
  {
    id: 10,
    item_name: 'مكمل الكالسيوم',
    category: 'nutraceutical',
    quantity: 60,
    expiration_date: '2025-07-05', // منتهي الصلاحية (منذ 10 أيام)
    batch_number: 'C010',
    supplier: 'شركة الفيتامينات',
    created_at: '2024-07-05T16:20:00Z'
  },
  // إضافة منتجات أخرى لتحسين الإحصائيات
  {
    id: 11,
    item_name: 'إيبوبروفين 400mg',
    category: 'medicine',
    quantity: 75,
    expiration_date: '2026-10-15', // آمن
    batch_number: 'I011',
    supplier: 'شركة الأدوية المصرية',
    created_at: '2024-08-01T10:00:00Z'
  },
  {
    id: 12,
    item_name: 'أوميجا 3',
    category: 'nutraceutical',
    quantity: 40,
    expiration_date: '2026-05-20', // آمن
    batch_number: 'O012',
    supplier: 'شركة الفيتامينات',
    created_at: '2024-08-05T14:30:00Z'
  },
  {
    id: 13,
    item_name: 'حفاضات أطفال',
    category: 'mom&baby',
    quantity: 80,
    expiration_date: '2027-01-10', // آمن
    batch_number: 'D013',
    supplier: 'شركة منتجات الأطفال',
    created_at: '2024-08-10T09:15:00Z'
  },
  {
    id: 14,
    item_name: 'كريم أساس',
    category: 'beauty',
    quantity: 25,
    expiration_date: '2025-12-15', // آمن
    batch_number: 'F014',
    supplier: 'شركة مستحضرات التجميل',
    created_at: '2024-08-15T16:45:00Z'
  },
  {
    id: 15,
    item_name: 'غسول الوجه',
    category: 'personal_care',
    quantity: 35,
    expiration_date: '2026-02-20', // آمن
    batch_number: 'W015',
    supplier: 'شركة العناية الشخصية',
    created_at: '2024-08-20T11:20:00Z'
  },
  // منتجات منتهية الصلاحية
  {
    id: 16,
    item_name: 'أنتيبيوتيك قديم',
    category: 'medicine',
    quantity: 20,
    expiration_date: '2025-06-01', // منتهي الصلاحية
    batch_number: 'A016',
    supplier: 'شركة الأدوية المصرية',
    created_at: '2024-01-01T08:00:00Z'
  },
  {
    id: 17,
    item_name: 'فيتامين ج منتهي',
    category: 'nutraceutical',
    quantity: 15,
    expiration_date: '2025-05-15', // منتهي الصلاحية
    batch_number: 'V017',
    supplier: 'شركة الفيتامينات',
    created_at: '2024-02-01T10:00:00Z'
  },
  {
    id: 18,
    item_name: 'كريم قديم للأطفال',
    category: 'mom&baby',
    quantity: 10,
    expiration_date: '2025-04-20', // منتهي الصلاحية
    batch_number: 'C018',
    supplier: 'شركة منتجات الأطفال',
    created_at: '2024-01-15T12:00:00Z'
  },
  {
    id: 19,
    item_name: 'أحمر شفاه منتهي',
    category: 'beauty',
    quantity: 8,
    expiration_date: '2025-03-10', // منتهي الصلاحية
    batch_number: 'L019',
    supplier: 'شركة مستحضرات التجميل',
    created_at: '2024-01-20T14:30:00Z'
  },
  {
    id: 20,
    item_name: 'صابون قديم',
    category: 'personal_care',
    quantity: 12,
    expiration_date: '2025-02-28', // منتهي الصلاحية
    batch_number: 'S020',
    supplier: 'شركة العناية الشخصية',
    created_at: '2024-01-25T16:00:00Z'
  }
];

// دالة لحساب الإحصائيات
export const calculateStats = () => {
  const today = new Date();
  const sevenDaysFromNow = new Date();
  sevenDaysFromNow.setDate(today.getDate() + 7);

  let totalItems = 0;
  let expiredItems = 0;
  let expiringSoonItems = 0;
  let categoryStats = {
    medicine: 0,
    'mom&baby': 0,
    personal_care: 0,
    nutraceutical: 0,
    beauty: 0
  };

  mockItems.forEach(item => {
    totalItems += item.quantity;
    const expirationDate = new Date(item.expiration_date);

    if (expirationDate < today) {
      expiredItems += item.quantity;
    } else if (expirationDate <= sevenDaysFromNow) {
      expiringSoonItems += item.quantity;
    }

    categoryStats[item.category] += item.quantity;
  });

  return {
    totalItems,
    expiredItems,
    expiringSoonItems,
    safeItems: totalItems - expiredItems - expiringSoonItems,
    categoryStats,
    totalProducts: mockItems.length
  };
};

// دالة للحصول على المنتجات المنتهية الصلاحية
export const getExpiredItems = () => {
  const today = new Date();
  return mockItems.filter(item => new Date(item.expiration_date) < today);
};

// دالة للحصول على المنتجات التي تنتهي قريباً
export const getExpiringSoonItems = (days = 7) => {
  const today = new Date();
  const futureDate = new Date();
  futureDate.setDate(today.getDate() + days);

  return mockItems.filter(item => {
    const expirationDate = new Date(item.expiration_date);
    return expirationDate >= today && expirationDate <= futureDate;
  });
};

// دالة للحصول على المنتجات حسب الفئة
export const getItemsByCategory = (category) => {
  return mockItems.filter(item => item.category === category);
};

// دالة لإضافة منتج جديد
export const addNewItem = (itemData) => {
  const newItem = {
    id: Math.max(...mockItems.map(item => item.id)) + 1,
    ...itemData,
    created_at: new Date().toISOString()
  };
  mockItems.push(newItem);
  return newItem;
};

// دالة لحذف منتج
export const deleteItem = (id) => {
  const index = mockItems.findIndex(item => item.id === id);
  if (index > -1) {
    mockItems.splice(index, 1);
    return true;
  }
  return false;
};

// دالة لتحديث منتج
export const updateItem = (id, updatedData) => {
  const index = mockItems.findIndex(item => item.id === id);
  if (index > -1) {
    mockItems[index] = { ...mockItems[index], ...updatedData };
    return mockItems[index];
  }
  return null;
};
