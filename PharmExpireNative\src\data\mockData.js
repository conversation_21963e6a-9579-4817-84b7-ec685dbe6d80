/**
 * بيانات وهمية للتطبيق
 * Mock data for the app
 */

// بيانات وهمية للمنتجات
export const mockItems = [
  {
    id: 1,
    item_name: 'باراسيتامول 500mg',
    category: 'medicine',
    quantity: 50,
    expiration_date: '2024-08-15',
    batch_number: 'B001',
    supplier: 'شركة الأدوية المصرية',
    created_at: '2024-01-15T10:00:00Z'
  },
  {
    id: 2,
    item_name: 'فيتامين د 1000 وحدة',
    category: 'wellness',
    quantity: 30,
    expiration_date: '2024-12-20',
    batch_number: 'V002',
    supplier: 'شركة الفيتامينات',
    created_at: '2024-02-10T14:30:00Z'
  },
  {
    id: 3,
    item_name: 'كريم مرطب للأطفال',
    category: 'mom&baby',
    quantity: 25,
    expiration_date: '2024-07-30',
    batch_number: 'C003',
    supplier: 'شركة منتجات الأطفال',
    created_at: '2024-03-05T09:15:00Z'
  },
  {
    id: 4,
    item_name: 'شامبو طبيعي',
    category: 'cosmetic',
    quantity: 40,
    expiration_date: '2025-01-10',
    batch_number: 'S004',
    supplier: 'شركة مستحضرات التجميل',
    created_at: '2024-04-12T16:45:00Z'
  },
  {
    id: 5,
    item_name: 'عسل طبيعي',
    category: 'food',
    quantity: 15,
    expiration_date: '2024-07-25',
    batch_number: 'H005',
    supplier: 'مناحل الطبيعة',
    created_at: '2024-05-20T11:20:00Z'
  },
  {
    id: 6,
    item_name: 'أسبرين 100mg',
    category: 'medicine',
    quantity: 100,
    expiration_date: '2024-06-30',
    batch_number: 'A006',
    supplier: 'شركة الأدوية المصرية',
    created_at: '2024-01-25T08:30:00Z'
  }
];

// دالة لحساب الإحصائيات
export const calculateStats = () => {
  const today = new Date();
  const sevenDaysFromNow = new Date();
  sevenDaysFromNow.setDate(today.getDate() + 7);

  let totalItems = 0;
  let expiredItems = 0;
  let expiringSoonItems = 0;
  let categoryStats = {
    food: 0,
    medicine: 0,
    'mom&baby': 0,
    cosmetic: 0,
    wellness: 0
  };

  mockItems.forEach(item => {
    totalItems += item.quantity;
    const expirationDate = new Date(item.expiration_date);
    
    if (expirationDate < today) {
      expiredItems += item.quantity;
    } else if (expirationDate <= sevenDaysFromNow) {
      expiringSoonItems += item.quantity;
    }
    
    categoryStats[item.category] += item.quantity;
  });

  return {
    totalItems,
    expiredItems,
    expiringSoonItems,
    safeItems: totalItems - expiredItems - expiringSoonItems,
    categoryStats,
    totalProducts: mockItems.length
  };
};

// دالة للحصول على المنتجات المنتهية الصلاحية
export const getExpiredItems = () => {
  const today = new Date();
  return mockItems.filter(item => new Date(item.expiration_date) < today);
};

// دالة للحصول على المنتجات التي تنتهي قريباً
export const getExpiringSoonItems = (days = 7) => {
  const today = new Date();
  const futureDate = new Date();
  futureDate.setDate(today.getDate() + days);
  
  return mockItems.filter(item => {
    const expirationDate = new Date(item.expiration_date);
    return expirationDate >= today && expirationDate <= futureDate;
  });
};

// دالة للحصول على المنتجات حسب الفئة
export const getItemsByCategory = (category) => {
  return mockItems.filter(item => item.category === category);
};

// دالة لإضافة منتج جديد
export const addNewItem = (itemData) => {
  const newItem = {
    id: Math.max(...mockItems.map(item => item.id)) + 1,
    ...itemData,
    created_at: new Date().toISOString()
  };
  mockItems.push(newItem);
  return newItem;
};

// دالة لحذف منتج
export const deleteItem = (id) => {
  const index = mockItems.findIndex(item => item.id === id);
  if (index > -1) {
    mockItems.splice(index, 1);
    return true;
  }
  return false;
};

// دالة لتحديث منتج
export const updateItem = (id, updatedData) => {
  const index = mockItems.findIndex(item => item.id === id);
  if (index > -1) {
    mockItems[index] = { ...mockItems[index], ...updatedData };
    return mockItems[index];
  }
  return null;
};
