/**
 * بيانات وهمية للتطبيق
 * Mock data for the app
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

// مفتاح التخزين
const STORAGE_KEY = 'pharmacy_items';

// قاعدة بيانات المنتجات - تبدأ فارغة
export let mockItems = [
  // سيتم إضافة المنتجات هنا عندما يضيفها المستخدم
];

// دالة لحفظ البيانات
const saveData = async () => {
  try {
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(mockItems));
  } catch (error) {
    console.error('خطأ في حفظ البيانات:', error);
  }
};

// دالة لتحميل البيانات
export const loadData = async () => {
  try {
    const data = await AsyncStorage.getItem(STORAGE_KEY);
    if (data) {
      const parsedData = JSON.parse(data);
      mockItems.length = 0; // مسح البيانات الحالية
      mockItems.push(...parsedData); // إضافة البيانات المحفوظة
      return parsedData;
    }
    return [];
  } catch (error) {
    console.error('خطأ في تحميل البيانات:', error);
    return [];
  }
};

// دالة لحساب الإحصائيات
export const calculateStats = () => {
  const today = new Date();
  const sevenDaysFromNow = new Date();
  sevenDaysFromNow.setDate(today.getDate() + 7);

  let totalItems = 0;
  let expiredItems = 0;
  let expiringSoonItems = 0;
  let categoryStats = {
    medicine: 0,
    'mom&baby': 0,
    personal_care: 0,
    nutraceutical: 0,
    beauty: 0
  };

  mockItems.forEach(item => {
    totalItems += item.quantity;
    const expirationDate = new Date(item.expiration_date);

    if (expirationDate < today) {
      expiredItems += item.quantity;
    } else if (expirationDate <= sevenDaysFromNow) {
      expiringSoonItems += item.quantity;
    }

    categoryStats[item.category] += item.quantity;
  });

  return {
    totalItems,
    expiredItems,
    expiringSoonItems,
    safeItems: totalItems - expiredItems - expiringSoonItems,
    categoryStats,
    totalProducts: mockItems.length
  };
};

// دالة للحصول على المنتجات المنتهية الصلاحية
export const getExpiredItems = () => {
  const today = new Date();
  return mockItems.filter(item => new Date(item.expiration_date) < today);
};

// دالة للحصول على المنتجات التي تنتهي قريباً
export const getExpiringSoonItems = (days = 7) => {
  const today = new Date();
  const futureDate = new Date();
  futureDate.setDate(today.getDate() + days);

  return mockItems.filter(item => {
    const expirationDate = new Date(item.expiration_date);
    return expirationDate >= today && expirationDate <= futureDate;
  });
};

// دالة للحصول على المنتجات حسب الفئة
export const getItemsByCategory = (category) => {
  return mockItems.filter(item => item.category === category);
};

// دالة لإضافة منتج جديد
export const addNewItem = async (itemData) => {
  // إنشاء ID جديد - إذا كانت القائمة فارغة، ابدأ من 1
  const newId = mockItems.length === 0 ? 1 : Math.max(...mockItems.map(item => item.id)) + 1;

  const newItem = {
    id: newId,
    ...itemData,
    created_at: new Date().toISOString()
  };
  mockItems.push(newItem);
  await saveData(); // حفظ البيانات
  return newItem;
};

// دالة لحذف منتج
export const deleteItem = async (id) => {
  const index = mockItems.findIndex(item => item.id === id);
  if (index > -1) {
    mockItems.splice(index, 1);
    await saveData(); // حفظ البيانات
    return true;
  }
  return false;
};

// دالة لتحديث منتج
export const updateItem = async (id, updatedData) => {
  const index = mockItems.findIndex(item => item.id === id);
  if (index > -1) {
    mockItems[index] = { ...mockItems[index], ...updatedData };
    await saveData(); // حفظ البيانات
    return mockItems[index];
  }
  return null;
};

// دالة لإعادة تعيين قاعدة البيانات (مفيدة للاختبار)
export const resetDatabase = () => {
  mockItems.length = 0; // مسح جميع العناصر
  return true;
};




