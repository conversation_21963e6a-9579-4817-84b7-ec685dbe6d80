import React, { useState } from 'react';
import { Home, Package, Calendar, AlertTriangle, Plus, Menu, X, FileText } from 'lucide-react';
import Dashboard from './components/Dashboard';
import ItemList from './components/ItemList';
import ItemForm from './components/ItemForm';
import NotificationBanner from './components/NotificationBanner';
import ExportImport from './components/ExportImport';

function App() {
  const [currentView, setCurrentView] = useState('dashboard');
  const [editingItem, setEditingItem] = useState(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const navigation = [
    { id: 'dashboard', name: 'Dashboard', icon: Home },
    { id: 'all', name: 'All Items', icon: Package },
    { id: 'expiring', name: 'Expiring Soon', icon: Calendar },
    { id: 'expired', name: 'Expired Items', icon: AlertTriangle },
    { id: 'add', name: 'Add Item', icon: Plus },
    { id: 'export', name: 'Export/Import', icon: FileText },
  ];

  const handleNavigate = (view) => {
    setCurrentView(view);
    setEditingItem(null);
    setSidebarOpen(false);
  };

  const handleEdit = (item) => {
    setEditingItem(item);
    setCurrentView('add');
  };

  const handleSave = () => {
    setEditingItem(null);
    setCurrentView('dashboard');
  };

  const handleCancel = () => {
    setEditingItem(null);
    setCurrentView('dashboard');
  };

  const renderContent = () => {
    switch (currentView) {
      case 'dashboard':
        return <Dashboard onNavigate={handleNavigate} />;
      case 'all':
        return <ItemList filter="all" onEdit={handleEdit} onAdd={() => handleNavigate('add')} />;
      case 'expiring':
        return <ItemList filter="expiring" onEdit={handleEdit} onAdd={() => handleNavigate('add')} />;
      case 'expired':
        return <ItemList filter="expired" onEdit={handleEdit} onAdd={() => handleNavigate('add')} />;
      case 'add':
        return <ItemForm item={editingItem} onSave={handleSave} onCancel={handleCancel} />;
      case 'export':
        return <ExportImport onImportComplete={() => handleNavigate('dashboard')} />;
      default:
        return <Dashboard onNavigate={handleNavigate} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`}>
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="bg-blue-600 p-2 rounded-lg">
              <Package className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-bold text-gray-900">PharmExpire</h1>
              <p className="text-xs text-gray-600">Management System</p>
            </div>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <nav className="mt-6 px-3">
          <ul className="space-y-1">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = currentView === item.id;

              return (
                <li key={item.id}>
                  <button
                    onClick={() => handleNavigate(item.id)}
                    className={`w-full flex items-center gap-3 px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                      isActive
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                    }`}
                  >
                    <Icon className="h-5 w-5" />
                    {item.name}
                  </button>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Footer */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
          <p className="text-xs text-gray-500 text-center">
            © 2024 PharmExpire Management
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Bar */}
        <div className="bg-white shadow-sm border-b border-gray-200 lg:hidden">
          <div className="flex items-center justify-between h-16 px-4">
            <button
              onClick={() => setSidebarOpen(true)}
              className="text-gray-400 hover:text-gray-600"
            >
              <Menu className="h-6 w-6" />
            </button>
            <div className="flex items-center gap-2">
              <Package className="h-6 w-6 text-blue-600" />
              <span className="font-bold text-gray-900">PharmExpire</span>
            </div>
            <div className="w-6"></div>
          </div>
        </div>

        {/* Content */}
        <main className="flex-1 overflow-auto p-6">
          <NotificationBanner onNavigate={handleNavigate} />
          {renderContent()}
        </main>
      </div>

      {/* Overlay for mobile sidebar */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
}

export default App;
