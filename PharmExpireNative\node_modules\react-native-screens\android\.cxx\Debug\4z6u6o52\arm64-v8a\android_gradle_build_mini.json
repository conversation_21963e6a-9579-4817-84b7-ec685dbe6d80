{"buildFiles": ["D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\4z6u6o52\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\4z6u6o52\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-screens\\android\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\4z6u6o52\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\4z6u6o52\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"rnscreens::@6890427a1f51a3e7e1df": {"artifactName": "rnscreens", "abi": "arm64-v8a", "output": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx\\Debug\\4z6u6o52\\obj\\arm64-v8a\\librnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so"]}}}