1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.pharmexpirenative"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:3:5-67
11-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:3:22-64
12    <!--
13    This manifest file is used only by Gradle to configure debug-only capabilities
14    for React Native Apps.
15    -->
16    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
16-->[com.facebook.react:react-android:0.80.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d221509c521a46e74f6572064742339\transformed\react-android-0.80.1-debug\AndroidManifest.xml:16:5-78
16-->[com.facebook.react:react-android:0.80.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d221509c521a46e74f6572064742339\transformed\react-android-0.80.1-debug\AndroidManifest.xml:16:22-75
17
18    <permission
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
19        android:name="com.pharmexpirenative.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.pharmexpirenative.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
23
24    <application
24-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:5:5-25:19
25        android:name="com.pharmexpirenative.MainApplication"
25-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:6:7-38
26        android:allowBackup="false"
26-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:10:7-34
27        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
27-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
28        android:debuggable="true"
29        android:extractNativeLibs="false"
30        android:icon="@mipmap/ic_launcher"
30-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:8:7-41
31        android:label="@string/app_name"
31-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:7:7-39
32        android:roundIcon="@mipmap/ic_launcher_round"
32-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:9:7-52
33        android:supportsRtl="true"
33-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:12:7-33
34        android:testOnly="true"
35        android:theme="@style/AppTheme"
35-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:11:7-38
36        android:usesCleartextTraffic="true" >
36-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\debug\AndroidManifest.xml:6:9-44
37        <activity
37-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:13:7-24:18
38            android:name="com.pharmexpirenative.MainActivity"
38-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:14:9-37
39            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
39-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:16:9-118
40            android:exported="true"
40-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:19:9-32
41            android:label="@string/app_name"
41-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:15:9-41
42            android:launchMode="singleTask"
42-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:17:9-40
43            android:windowSoftInputMode="adjustResize" >
43-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:18:9-51
44            <intent-filter>
44-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:20:9-23:25
45                <action android:name="android.intent.action.MAIN" />
45-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:21:13-65
45-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:21:21-62
46
47                <category android:name="android.intent.category.LAUNCHER" />
47-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:22:13-73
47-->D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\AndroidManifest.xml:22:23-70
48            </intent-filter>
49        </activity>
50        <activity
50-->[com.facebook.react:react-android:0.80.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d221509c521a46e74f6572064742339\transformed\react-android-0.80.1-debug\AndroidManifest.xml:19:9-21:40
51            android:name="com.facebook.react.devsupport.DevSettingsActivity"
51-->[com.facebook.react:react-android:0.80.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d221509c521a46e74f6572064742339\transformed\react-android-0.80.1-debug\AndroidManifest.xml:20:13-77
52            android:exported="false" />
52-->[com.facebook.react:react-android:0.80.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d221509c521a46e74f6572064742339\transformed\react-android-0.80.1-debug\AndroidManifest.xml:21:13-37
53
54        <provider
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
55            android:name="androidx.startup.InitializationProvider"
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
56            android:authorities="com.pharmexpirenative.androidx-startup"
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
57            android:exported="false" >
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
58            <meta-data
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
59                android:name="androidx.emoji2.text.EmojiCompatInitializer"
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
60                android:value="androidx.startup" />
60-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
61            <meta-data
61-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\22777817996210683c00c27568dea84f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
62                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
62-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\22777817996210683c00c27568dea84f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
63                android:value="androidx.startup" />
63-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\22777817996210683c00c27568dea84f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
64            <meta-data
64-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
65                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
66                android:value="androidx.startup" />
66-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
67        </provider>
68
69        <receiver
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
70            android:name="androidx.profileinstaller.ProfileInstallReceiver"
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
71            android:directBootAware="false"
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
72            android:enabled="true"
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
73            android:exported="true"
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
74            android:permission="android.permission.DUMP" >
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
76                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
77            </intent-filter>
78            <intent-filter>
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
79                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
80            </intent-filter>
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
82                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
83            </intent-filter>
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
85                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
86            </intent-filter>
87        </receiver>
88
89        <meta-data
89-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\363f04887ef37f5443484b40dba2058e\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
90            android:name="com.facebook.soloader.enabled"
90-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\363f04887ef37f5443484b40dba2058e\transformed\soloader-0.12.1\AndroidManifest.xml:13:13-57
91            android:value="false" />
91-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\363f04887ef37f5443484b40dba2058e\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
92    </application>
93
94</manifest>
