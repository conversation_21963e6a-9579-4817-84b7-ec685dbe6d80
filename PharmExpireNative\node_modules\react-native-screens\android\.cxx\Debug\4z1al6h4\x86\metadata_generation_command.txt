                        -HD:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-screens\android
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-<PERSON><PERSON>DROID_PLATFORM=android-24
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.1.12297006
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.1.12297006
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.1.12297006\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-screens\android\build\intermediates\cxx\Debug\4z1al6h4\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-screens\android\build\intermediates\cxx\Debug\4z1al6h4\obj\x86
-DCMAKE_BUILD_TYPE=Debug
-DCMAKE_FIND_ROOT_PATH=D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-screens\android\.cxx\Debug\4z1al6h4\prefab\x86\prefab
-BD:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-screens\android\.cxx\Debug\4z1al6h4\x86
-GNinja
-DANDROID_STL=c++_shared
-DRNS_NEW_ARCH_ENABLED=true
-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON
                        Build command args: []
                        Version: 2