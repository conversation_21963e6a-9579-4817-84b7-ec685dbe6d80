[{"directory": "D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native-screens/android/.cxx/Debug/4z6u6o52/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -I\"D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native-screens/android/../cpp\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\9858fa4c5fe43baa2165615811ad4891\\react-native-screens\\cpp\\RNScreensTurboModule.cpp.o -c \"D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-screens\\cpp\\RNScreensTurboModule.cpp\"", "file": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-screens\\cpp\\RNScreensTurboModule.cpp"}, {"directory": "D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native-screens/android/.cxx/Debug/4z6u6o52/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -I\"D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native-screens/android/../cpp\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\src\\main\\cpp\\jni-adapter.cpp.o -c \"D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\jni-adapter.cpp\"", "file": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\jni-adapter.cpp"}]