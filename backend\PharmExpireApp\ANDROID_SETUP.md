# 📱 PharmExpire Android App Setup Guide

## 🎯 **Overview**
This is the Android mobile app for the PharmExpire Pharmacy Management System. It connects to the same backend API as the web application.

## 📋 **Prerequisites**

### **Required Software:**
1. **Node.js** (18+) - Already installed ✅
2. **Android Studio** - Download from [developer.android.com](https://developer.android.com/studio)
3. **Java Development Kit (JDK)** - Version 11 or higher
4. **Android SDK** - Installed via Android Studio

### **Android Studio Setup:**
1. Install Android Studio
2. Open Android Studio and install:
   - Android SDK Platform 33 (or latest)
   - Android SDK Build-Tools
   - Android Emulator
3. Create an Android Virtual Device (AVD) or connect a physical device

## 🔧 **Configuration**

### **1. Update API Endpoint**
Before running the app, you need to update the API endpoint in the app to point to your backend server.

**File:** `src/services/api.js`
```javascript
// Update this IP address to your computer's local IP address
const API_BASE_URL = 'http://YOUR_LOCAL_IP:3001/api';
```

**To find your local IP address:**
- **Windows:** Run `ipconfig` in Command Prompt, look for "IPv4 Address"
- **Mac/Linux:** Run `ifconfig` in Terminal, look for "inet" address

**Example:**
```javascript
const API_BASE_URL = 'http://*************:3001/api';
```

### **2. Ensure Backend is Running**
Make sure your backend server is running on port 3001:
```bash
cd ../../  # Go back to project root
cd backend
npm run dev
```

## 🚀 **Running the App**

### **Method 1: Using Android Emulator**
1. **Start Android Studio**
2. **Open AVD Manager** (Tools → AVD Manager)
3. **Start an emulator** (click the play button)
4. **Run the app:**
   ```bash
   npx react-native run-android
   ```

### **Method 2: Using Physical Device**
1. **Enable Developer Options** on your Android device:
   - Go to Settings → About Phone
   - Tap "Build Number" 7 times
   - Go back to Settings → Developer Options
   - Enable "USB Debugging"
2. **Connect device** via USB
3. **Run the app:**
   ```bash
   npx react-native run-android
   ```

## 📱 **App Features**

### **🏠 Dashboard**
- Real-time inventory statistics
- Category breakdown
- Quick action buttons
- Color-coded status indicators

### **📦 Items Management**
- View all items in a scrollable list
- Search and filter functionality
- Add new items with form validation
- Edit existing items
- Delete items with confirmation

### **🔔 Expiration Tracking**
- **🔴 Red:** Expired items
- **🟡 Yellow:** Items expiring within 7 days
- **🟢 Green:** Safe items (more than 7 days)

### **📋 Categories**
- 🍎 Food
- 💊 Medicine
- 👶 Mom & Baby
- 💄 Cosmetic
- 🌿 Wellness

## 🛠 **Development Commands**

```bash
# Install dependencies
npm install

# Start Metro bundler
npx react-native start

# Run on Android
npx react-native run-android

# Clean build (if needed)
cd android && ./gradlew clean && cd ..

# Reset Metro cache
npx react-native start --reset-cache
```

## 🔍 **Troubleshooting**

### **Common Issues:**

#### **1. Metro bundler not starting**
```bash
npx react-native start --reset-cache
```

#### **2. Build errors**
```bash
cd android
./gradlew clean
cd ..
npx react-native run-android
```

#### **3. Network connection issues**
- Ensure your phone/emulator and computer are on the same WiFi network
- Check that the backend server is running on port 3001
- Verify the IP address in `src/services/api.js` is correct

#### **4. Android SDK issues**
- Open Android Studio
- Go to Tools → SDK Manager
- Ensure Android SDK Platform and Build-Tools are installed

#### **5. USB debugging not working**
- Try different USB cable
- Enable "File Transfer" mode on your device
- Revoke USB debugging authorizations and try again

### **Testing API Connection:**
You can test if the API is accessible from your device by opening a browser on your phone and navigating to:
```
http://YOUR_LOCAL_IP:3001/api/health
```

You should see a JSON response with status "OK".

## 📊 **App Structure**

```
src/
├── components/          # Reusable UI components
├── screens/            # Main app screens
│   ├── DashboardScreen.js
│   ├── ItemListScreen.js
│   ├── AddItemScreen.js
│   └── ItemDetailScreen.js
├── navigation/         # Navigation configuration
│   └── AppNavigator.js
├── services/          # API services
│   └── api.js
└── utils/            # Utility functions
    └── dateUtils.js
```

## 🎨 **UI Design**
- **Material Design** components
- **Blue color scheme** (#2563eb)
- **Responsive layout** for different screen sizes
- **Touch-friendly** buttons and interactions
- **Status-based colors** for expiration alerts

## 🔄 **Data Synchronization**
The mobile app shares the same database as the web application through the REST API. Changes made in either the mobile app or web app will be reflected in both.

## 📱 **Building for Release**

### **Generate APK:**
```bash
cd android
./gradlew assembleRelease
```

The APK will be generated at:
`android/app/build/outputs/apk/release/app-release.apk`

### **Generate AAB (for Google Play Store):**
```bash
cd android
./gradlew bundleRelease
```

## 🆘 **Support**

If you encounter any issues:
1. Check that all prerequisites are installed
2. Ensure the backend server is running
3. Verify network connectivity
4. Check the React Native logs for error messages
5. Try cleaning and rebuilding the project

## 🎉 **Success!**
Once everything is set up correctly, you'll have a fully functional Android app for managing your pharmacy inventory with real-time expiration tracking!

---

**Happy Mobile Development! 📱💊**
