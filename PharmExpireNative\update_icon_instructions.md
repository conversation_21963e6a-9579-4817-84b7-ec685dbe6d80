# 📱 تعليمات تحديث أيقونة التطبيق

## 🎯 الخطوات المطلوبة:

### 1. إنشاء الأيقونات بالأحجام المختلفة:

استخدم إحدى هذه المواقع لإنشاء الأيقونات:
- **App Icon Generator**: https://appicon.co/
- **Icon Kitchen**: https://icon.kitchen/
- **Android Asset Studio**: https://romannurik.github.io/AndroidAssetStudio/icons-launcher.html

### 2. استبدال الملفات:

ضع الملفات الجديدة في هذه المجلدات:

```
android/app/src/main/res/
├── mipmap-mdpi/ic_launcher.png      (48x48 px)
├── mipmap-hdpi/ic_launcher.png      (72x72 px)  
├── mipmap-xhdpi/ic_launcher.png     (96x96 px)
├── mipmap-xxhdpi/ic_launcher.png    (144x144 px)
└── mipmap-xxxhdpi/ic_launcher.png   (192x192 px)
```

### 3. تنظيف وإعادة البناء:

```bash
# تنظيف المشروع
cd android
./gradlew clean

# العودة للمجلد الرئيسي
cd ..

# إعادة تشغيل التطبيق
npx react-native run-android
```

## 📐 مواصفات الأيقونة المثالية:

- **الحجم الأساسي**: 512x512 بكسل
- **التنسيق**: PNG مع خلفية شفافة
- **التصميم**: بسيط وواضح
- **الموضوع**: متعلق بالصيدلية (صليب أحمر، حبوب، إلخ)
- **الألوان**: متباينة وجذابة

## 💡 نصائح التصميم:

- استخدم ألوان تتناسب مع موضوع الصيدلية (أخضر، أزرق، أبيض)
- اجعل الأيقونة بسيطة ومميزة
- تأكد من وضوحها في الأحجام الصغيرة
- استخدم رموز طبية أو صيدلانية مناسبة
- تجنب النصوص الصغيرة التي قد لا تظهر بوضوح

## ⚠️ ملاحظات مهمة:

1. **احتفظ بنسخة احتياطية** من الأيقونات الأصلية
2. **تأكد من الأحجام الصحيحة** لكل مجلد
3. **استخدم PNG** بدلاً من JPG للشفافية
4. **أعد تشغيل التطبيق** بعد تغيير الأيقونات
5. **اختبر على أجهزة مختلفة** للتأكد من الوضوح

## 🎨 أفكار للأيقونة:

- صليب أحمر مع خلفية بيضاء أو زرقاء
- حبوب دواء ملونة
- زجاجة دواء مع صليب
- رمز الصيدلية التقليدي (الثعبان والكأس)
- تقويم مع تاريخ انتهاء الصلاحية
- مزيج من الرموز الطبية والتقنية
