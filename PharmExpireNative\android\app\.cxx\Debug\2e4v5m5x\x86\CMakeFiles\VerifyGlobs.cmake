# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# input_SRC at D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:55 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/build/generated/autolinking/src/main/jni/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- G<PERSON>O<PERSON> mismatch!")
  file(TOUCH_NOCREATE "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/CMakeFiles/cmake.verify_globs")
endif()

# override_cpp_SRC at D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:50 (file)
# input_SRC at D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:55 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/*.cpp")
set(OLD_GLOB
  "D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/CMakeFiles/cmake.verify_globs")
endif()
