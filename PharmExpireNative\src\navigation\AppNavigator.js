import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';

// استيراد الشاشات
import DashboardScreen from '../screens/DashboardScreen';
import ItemListScreen from '../screens/ItemListScreen';
import AddItemScreen from '../screens/AddItemScreen';
import ItemDetailScreen from '../screens/ItemDetailScreen';
import ReportsScreen from '../screens/ReportsScreen';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// مكدس التنقل للمنتجات
const ItemsStack = () => {
  return (
    <Stack.Navigator>
      <Stack.Screen
        name="ItemList"
        component={ItemListScreen}
        options={{
          title: 'جميع المنتجات',
          headerStyle: { backgroundColor: '#2563eb' },
          headerTintColor: '#fff',
          headerTitleStyle: { fontWeight: 'bold' }
        }}
      />
      <Stack.Screen
        name="ItemDetail"
        component={ItemDetailScreen}
        options={{
          title: 'تفاصيل المنتج',
          headerStyle: { backgroundColor: '#2563eb' },
          headerTintColor: '#fff',
          headerTitleStyle: { fontWeight: 'bold' }
        }}
      />
      <Stack.Screen
        name="AddItem"
        component={AddItemScreen}
        options={{
          title: 'إضافة منتج',
          headerStyle: { backgroundColor: '#2563eb' },
          headerTintColor: '#fff',
          headerTitleStyle: { fontWeight: 'bold' }
        }}
      />
    </Stack.Navigator>
  );
};

// التنقل الرئيسي بالتبويبات
const TabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Dashboard') {
            iconName = 'dashboard';
          } else if (route.name === 'Items') {
            iconName = 'inventory';
          } else if (route.name === 'Add') {
            iconName = 'add-circle';
          } else if (route.name === 'Reports') {
            iconName = 'assessment';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#2563eb',
        tabBarInactiveTintColor: 'gray',
        headerStyle: {
          backgroundColor: '#2563eb',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen
        name="Dashboard"
        component={DashboardScreen}
        options={{
          title: 'لوحة التحكم',
          tabBarLabel: 'الرئيسية'
        }}
      />
      <Tab.Screen
        name="Items"
        component={ItemsStack}
        options={{
          headerShown: false,
          tabBarLabel: 'المنتجات'
        }}
      />
      <Tab.Screen
        name="Add"
        component={AddItemScreen}
        options={{
          title: 'إضافة منتج جديد',
          tabBarLabel: 'إضافة'
        }}
      />
      <Tab.Screen
        name="Reports"
        component={ReportsScreen}
        options={{
          title: 'تقارير الصيدلية',
          tabBarLabel: 'التقارير'
        }}
      />
    </Tab.Navigator>
  );
};

// التنقل الرئيسي للتطبيق
const AppNavigator = () => {
  return (
    <NavigationContainer>
      <TabNavigator />
    </NavigationContainer>
  );
};

export default AppNavigator;
