<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\res"><file name="rn_edit_text_material" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\res\drawable\rn_edit_text_material.xml" qualifiers="" type="drawable"/><file name="icon1" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\res\mipmap-hdpi\icon1.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon1" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\res\mipmap-mdpi\icon1.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon1" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\res\mipmap-xhdpi\icon1.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon1" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\res\mipmap-xxhdpi\icon1.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon1" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\res\mipmap-xxxhdpi\icon1.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Expire Management Pro</string></file><file path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\generated\res\resValues\debug"><file path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\generated\res\resValues\debug\values\gradleResValues.xml" qualifiers=""><integer name="react_native_dev_server_port">8081</integer><string name="react_native_dev_server_ip" translatable="false">***************</string></file></source></dataSet><mergedItems/></merger>