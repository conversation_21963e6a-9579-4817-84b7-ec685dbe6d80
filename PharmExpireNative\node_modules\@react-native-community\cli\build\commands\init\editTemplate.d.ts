interface PlaceholderConfig {
    projectName: string;
    placeholderName: string;
    placeholderTitle?: string;
    projectTitle?: string;
    packageName?: string;
}
export declare function validatePackageName(packageName: string): void;
export declare function replaceNameInUTF8File(filePath: string, projectName: string, templateName: string): Promise<void>;
export declare function replacePlaceholderWithPackageName({ projectName, placeholderName, placeholderTitle, packageName, }: Omit<Required<PlaceholderConfig>, 'projectTitle'>): Promise<void>;
export declare function changePlaceholderInTemplate({ projectName, placeholderName, placeholderTitle, projectTitle, packageName, }: PlaceholderConfig): Promise<void>;
export {};
//# sourceMappingURL=editTemplate.d.ts.map