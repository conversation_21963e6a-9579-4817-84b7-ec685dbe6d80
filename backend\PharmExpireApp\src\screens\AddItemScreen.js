import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  Platform,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { itemsAPI } from '../services/api';
import { formatDateForInput, getCategoryIcon } from '../utils/dateUtils';

const AddItemScreen = ({ navigation, route }) => {
  const [formData, setFormData] = useState({
    item_name: '',
    category: '',
    expiration_date: '',
    notes: '',
    quantity: '1',
    location: '',
  });
  const [loading, setLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  const categories = ['Food', 'Medicine', 'Mom & Baby', 'Cosmetic', 'Wellness'];

  useEffect(() => {
    if (route?.params?.item) {
      const item = route.params.item;
      setIsEditing(true);
      setFormData({
        item_name: item.item_name || '',
        category: item.category || '',
        expiration_date: item.expiration_date ? formatDateForInput(item.expiration_date) : '',
        notes: item.notes || '',
        quantity: (item.quantity || 1).toString(),
        location: item.location || '',
      });
    }
  }, [route?.params?.item]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const validateForm = () => {
    if (!formData.item_name.trim()) {
      Alert.alert('Validation Error', 'Item name is required');
      return false;
    }
    if (!formData.category) {
      Alert.alert('Validation Error', 'Please select a category');
      return false;
    }
    if (!formData.expiration_date) {
      Alert.alert('Validation Error', 'Expiration date is required');
      return false;
    }
    if (isNaN(parseInt(formData.quantity)) || parseInt(formData.quantity) < 1) {
      Alert.alert('Validation Error', 'Quantity must be a positive number');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const submitData = {
        ...formData,
        quantity: parseInt(formData.quantity),
      };

      if (isEditing) {
        await itemsAPI.update(route.params.item.id, submitData);
        Alert.alert('Success', 'Item updated successfully');
      } else {
        await itemsAPI.create(submitData);
        Alert.alert('Success', 'Item added successfully');
      }

      navigation.goBack();
    } catch (error) {
      console.error('Error saving item:', error);
      Alert.alert('Error', error.response?.data?.error || 'Failed to save item');
    } finally {
      setLoading(false);
    }
  };

  const handleDateChange = (text) => {
    // Simple date formatting for YYYY-MM-DD
    let formatted = text.replace(/[^0-9]/g, '');
    if (formatted.length >= 4) {
      formatted = formatted.substring(0, 4) + '-' + formatted.substring(4);
    }
    if (formatted.length >= 7) {
      formatted = formatted.substring(0, 7) + '-' + formatted.substring(7, 9);
    }
    if (formatted.length > 10) {
      formatted = formatted.substring(0, 10);
    }
    handleInputChange('expiration_date', formatted);
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {isEditing ? 'Edit Item' : 'Add New Item'}
        </Text>
        <Text style={styles.headerSubtitle}>
          {isEditing ? 'Update item information' : 'Add a new item to your inventory'}
        </Text>
      </View>

      <View style={styles.form}>
        {/* Item Name */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Item Name *</Text>
          <TextInput
            style={styles.input}
            value={formData.item_name}
            onChangeText={(text) => handleInputChange('item_name', text)}
            placeholder="Enter item name"
            placeholderTextColor="#9ca3af"
          />
        </View>

        {/* Category */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Category *</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={formData.category}
              onValueChange={(value) => handleInputChange('category', value)}
              style={styles.picker}
            >
              <Picker.Item label="Select a category" value="" />
              {categories.map((category) => (
                <Picker.Item
                  key={category}
                  label={`${getCategoryIcon(category)} ${category}`}
                  value={category}
                />
              ))}
            </Picker>
          </View>
        </View>

        {/* Expiration Date */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Expiration Date *</Text>
          <TextInput
            style={styles.input}
            value={formData.expiration_date}
            onChangeText={handleDateChange}
            placeholder="YYYY-MM-DD"
            placeholderTextColor="#9ca3af"
            keyboardType="numeric"
            maxLength={10}
          />
          <Text style={styles.helpText}>Format: YYYY-MM-DD (e.g., 2024-12-31)</Text>
        </View>

        {/* Quantity and Location Row */}
        <View style={styles.row}>
          <View style={[styles.inputGroup, styles.halfWidth]}>
            <Text style={styles.label}>Quantity</Text>
            <TextInput
              style={styles.input}
              value={formData.quantity}
              onChangeText={(text) => handleInputChange('quantity', text)}
              placeholder="1"
              placeholderTextColor="#9ca3af"
              keyboardType="numeric"
            />
          </View>

          <View style={[styles.inputGroup, styles.halfWidth]}>
            <Text style={styles.label}>Location</Text>
            <TextInput
              style={styles.input}
              value={formData.location}
              onChangeText={(text) => handleInputChange('location', text)}
              placeholder="e.g., Shelf A"
              placeholderTextColor="#9ca3af"
            />
          </View>
        </View>

        {/* Notes */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Notes</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={formData.notes}
            onChangeText={(text) => handleInputChange('notes', text)}
            placeholder="Additional notes about this item"
            placeholderTextColor="#9ca3af"
            multiline
            numberOfLines={3}
            textAlignVertical="top"
          />
        </View>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.primaryButton]}
            onPress={handleSubmit}
            disabled={loading}
          >
            <Icon name="save" size={20} color="#fff" />
            <Text style={styles.primaryButtonText}>
              {loading ? 'Saving...' : (isEditing ? 'Update Item' : 'Add Item')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.secondaryButton]}
            onPress={() => navigation.goBack()}
            disabled={loading}
          >
            <Text style={styles.secondaryButtonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#6b7280',
  },
  form: {
    padding: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
    color: '#1f2937',
  },
  textArea: {
    height: 80,
    paddingTop: 12,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    backgroundColor: '#fff',
    overflow: 'hidden',
  },
  picker: {
    height: 50,
    color: '#1f2937',
  },
  helpText: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  buttonContainer: {
    marginTop: 20,
    gap: 12,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 8,
    gap: 8,
  },
  primaryButton: {
    backgroundColor: '#2563eb',
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  secondaryButtonText: {
    color: '#374151',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default AddItemScreen;
