{"version": 3, "names": ["TEMPLATE_PACKAGE_COMMUNITY", "TEMPLATE_PACKAGE_LEGACY", "TEMPLATE_PACKAGE_LEGACY_TYPESCRIPT", "TEMPLATE_COMMUNITY_REACT_NATIVE_VERSION"], "sources": ["../../../src/commands/init/constants.ts"], "sourcesContent": ["export const TEMPLATE_PACKAGE_COMMUNITY = '@react-native-community/template';\nexport const TEMPLATE_PACKAGE_LEGACY = 'react-native';\nexport const TEMPLATE_PACKAGE_LEGACY_TYPESCRIPT =\n  'react-native-template-typescript';\n\n// This version moved from inlining the template to using @react-native-community/template\nexport const TEMPLATE_COMMUNITY_REACT_NATIVE_VERSION = '0.75.0';\n"], "mappings": ";;;;;;AAAO,MAAMA,0BAA0B,GAAG,kCAAkC;AAAC;AACtE,MAAMC,uBAAuB,GAAG,cAAc;AAAC;AAC/C,MAAMC,kCAAkC,GAC7C,kCAAkC;;AAEpC;AAAA;AACO,MAAMC,uCAAuC,GAAG,QAAQ;AAAC"}