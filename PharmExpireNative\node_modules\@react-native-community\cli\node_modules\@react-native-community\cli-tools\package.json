{"name": "@react-native-community/cli-tools", "version": "19.1.0", "license": "MIT", "main": "build/index.js", "publishConfig": {"access": "public"}, "dependencies": {"@vscode/sudo-prompt": "^9.0.0", "appdirsjs": "^1.2.4", "chalk": "^4.1.2", "execa": "^5.0.0", "find-up": "^5.0.0", "launch-editor": "^2.9.1", "mime": "^2.4.1", "ora": "^5.4.1", "prompts": "^2.4.2", "semver": "^7.5.2"}, "devDependencies": {"@react-native-community/cli-types": "19.1.0", "@types/mime": "^2.0.1", "@types/node": "^18.0.0", "@types/prompts": "^2.4.4", "@types/shell-quote": "^1.7.1"}, "files": ["build", "!*.d.ts", "!*.map"], "homepage": "https://github.com/react-native-community/cli/tree/main/packages/cli-tools", "repository": {"type": "git", "url": "https://github.com/react-native-community/cli.git", "directory": "packages/tools"}, "gitHead": "8f3ecef6520713e24a67869afcde51f0c466e828"}