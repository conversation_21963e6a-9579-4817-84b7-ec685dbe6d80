@echo off
echo نسخ المشروع إلى مسار أقصر...
echo.

REM إنشاء مجلد في C:\
if not exist "C:\PharmApp" mkdir "C:\PharmApp"

REM نسخ المشروع
echo جاري نسخ الملفات...
robocopy "PharmExpireNative" "C:\PharmApp" /E /XD node_modules android\.gradle android\app\.cxx

echo.
echo تم نسخ المشروع إلى: C:\PharmApp
echo.

REM الانتقال للمجلد الجديد
cd /d "C:\PharmApp"

REM تثبيت التبعيات
echo تثبيت التبعيات...
call npm install

echo.
echo المشروع جاهز في: C:\PharmApp
echo لفتحه في Android Studio:
echo 1. افتح Android Studio
echo 2. اختر Open Project
echo 3. انتقل إلى: C:\PharmApp\android
echo.
pause
