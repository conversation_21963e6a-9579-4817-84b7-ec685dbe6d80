{"modules": {"NativeRNVectorIcons": {"type": "NativeModule", "aliasMap": {}, "enumMap": {}, "spec": {"eventEmitters": [], "methods": [{"name": "getImageForFont", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "PromiseTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}, "params": [{"name": "fontName", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation"}}, {"name": "glyph", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation"}}, {"name": "fontSize", "optional": false, "typeAnnotation": {"type": "NumberTypeAnnotation"}}, {"name": "color", "optional": false, "typeAnnotation": {"type": "NumberTypeAnnotation"}}]}}, {"name": "getImageForFontSync", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "StringTypeAnnotation"}, "params": [{"name": "fontName", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation"}}, {"name": "glyph", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation"}}, {"name": "fontSize", "optional": false, "typeAnnotation": {"type": "NumberTypeAnnotation"}}, {"name": "color", "optional": false, "typeAnnotation": {"type": "NumberTypeAnnotation"}}]}}, {"name": "loadFontWithFileName", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "PromiseTypeAnnotation", "elementType": {"type": "VoidTypeAnnotation"}}, "params": [{"name": "fontFileName", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation"}}, {"name": "extension", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation"}}]}}]}, "moduleName": "RNVectorIcons"}}}