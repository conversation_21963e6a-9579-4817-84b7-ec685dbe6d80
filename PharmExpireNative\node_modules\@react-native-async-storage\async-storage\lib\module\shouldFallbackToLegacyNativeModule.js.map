{"version": 3, "names": ["NativeModules", "shouldFallbackToLegacyNativeModule", "expoConstants", "modulesConstants", "ExponentConstants", "isLegacySdkVersion", "appOwnership", "executionEnvironment", "includes"], "sourceRoot": "../../src", "sources": ["shouldFallbackToLegacyNativeModule.ts"], "mappings": ";;AAAA,SAASA,aAAa,QAAQ,cAAc;AAE5C,OAAO,SAASC,kCAAkCA,CAAA,EAAY;EAC5D,MAAMC,aAAa,GACjBF,aAAa,CAAC,sBAAsB,CAAC,EAAEG,gBAAgB,EAAEC,iBAAiB;EAE5E,IAAIF,aAAa,EAAE;IACjB;AACJ;AACA;AACA;AACA;IACI,MAAMG,kBAAkB,GACtBH,aAAa,CAACI,YAAY,IAAI,CAACJ,aAAa,CAACK,oBAAoB;;IAEnE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IACEF,kBAAkB,IAClB,CAAC,aAAa,EAAE,YAAY,CAAC,CAACG,QAAQ,CAACN,aAAa,CAACK,oBAAoB,CAAC,EAC1E;MACA,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd", "ignoreList": []}