# 📱 تعليمات تشغيل تطبيق PharmExpire للأندرويد

## 🎉 **تم إنشاء التطبيق بنجاح!**

لقد تم إنشاء تطبيق أندرويد كامل ومتكامل لإدارة انتهاء صلاحية الصيدلية باستخدام React Native و Expo.

## 📱 **ميزات التطبيق**

### ✅ **الشاشات المتوفرة:**
- 🏠 **لوحة التحكم**: إحصائيات فورية وملخص شامل
- 📦 **قائمة المنتجات**: عرض وإدارة جميع المنتجات
- ➕ **إضافة منتج**: نموذج شامل لإضافة منتجات جديدة
- 📋 **تفاصيل المنتج**: عرض تفصيلي لكل منتج

### 🎨 **التصميم:**
- واجهة عربية كاملة
- ألوان ذكية (أحمر=منتهي، أصفر=ينتهي قريباً، أخضر=آمن)
- تصميم متجاوب ومتوافق مع الهواتف
- أيقونات واضحة وسهلة الاستخدام

### 🔧 **الوظائف:**
- إضافة وتعديل وحذف المنتجات
- البحث والتصفية المتقدم
- تتبع انتهاء الصلاحية
- إحصائيات شاملة
- تصنيف بالفئات

## 🚀 **طرق تشغيل التطبيق**

### **الطريقة 1: تطبيق Expo Go (الأسهل)**

#### **الخطوات:**
1. **حمّل تطبيق Expo Go** على هاتفك الأندرويد من Google Play Store
2. **تأكد من تشغيل الخادم الخلفي**:
   ```bash
   cd "d:\copy from kpi tracker app\expire management\backend"
   npm run dev
   ```
3. **شغّل التطبيق**:
   ```bash
   cd "d:\copy from kpi tracker app\expire management\PharmExpireAndroid"
   npx expo start
   ```
4. **امسح رمز QR** الذي يظهر باستخدام تطبيق Expo Go

### **الطريقة 2: بناء APK (للتثبيت المباشر)**

#### **المتطلبات:**
- Android Studio مثبت
- Java Development Kit (JDK)

#### **الخطوات:**
```bash
# بناء APK للإنتاج
npx expo build:android

# أو بناء محلي
npx expo run:android
```

### **الطريقة 3: تطبيق ويب متجاوب (بديل سريع)**

إذا واجهت صعوبة في تشغيل التطبيق الأصلي، يمكنك استخدام التطبيق الويب المتجاوب:

1. **افتح متصفح هاتفك**
2. **اذهب إلى**: `http://************:5173`
3. **أضف إلى الشاشة الرئيسية** للوصول السريع

## 🔧 **إعدادات مهمة**

### **تحديث عنوان IP:**
قبل تشغيل التطبيق، تأكد من تحديث عنوان IP في الملف:
`src/services/api.js`

```javascript
const API_BASE_URL = 'http://YOUR_IP_ADDRESS:3001/api';
```

### **العثور على عنوان IP:**
```bash
# في Windows
ipconfig

# ابحث عن IPv4 Address
```

## 📊 **البيانات التجريبية**

التطبيق يحتوي على بيانات تجريبية:
- 10 منتجات مختلفة
- فئات متنوعة (طعام، أدوية، أم وطفل، تجميل، صحة)
- حالات انتهاء صلاحية مختلفة

## 🎯 **الاستخدام**

### **إضافة منتج جديد:**
1. اضغط على تبويب "إضافة"
2. املأ المعلومات المطلوبة
3. اختر الفئة المناسبة
4. احفظ المنتج

### **البحث والتصفية:**
1. اذهب إلى تبويب "المنتجات"
2. استخدم شريط البحث
3. اختر فلتر (الكل، تنتهي قريباً، منتهية)

### **عرض التفاصيل:**
1. اضغط على أي منتج في القائمة
2. شاهد جميع التفاصيل
3. عدّل أو احذف حسب الحاجة

## 🔍 **استكشاف الأخطاء**

### **مشاكل شائعة:**

#### **لا يمكن الاتصال بالخادم:**
- تأكد من تشغيل الخادم الخلفي
- تحقق من عنوان IP الصحيح
- تأكد من أن الهاتف والكمبيوتر على نفس الشبكة

#### **التطبيق لا يعمل:**
- تأكد من تثبيت Expo Go
- أعد تشغيل الخادم
- امسح cache: `npx expo start -c`

#### **مشاكل في البناء:**
- تأكد من تثبيت Android Studio
- تحقق من إعدادات Java
- جرب التطبيق الويب كبديل

## 📱 **مقارنة الخيارات**

| الطريقة | السهولة | الأداء | التثبيت |
|---------|---------|---------|----------|
| **Expo Go** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | لا يحتاج |
| **APK** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | مطلوب |
| **تطبيق ويب** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | لا يحتاج |

## 🎊 **النتيجة النهائية**

✅ **تطبيق أندرويد كامل ومتكامل**
✅ **واجهة عربية احترافية**
✅ **جميع الميزات المطلوبة**
✅ **تصميم متجاوب وجميل**
✅ **سهل الاستخدام والتنقل**

**تطبيقك جاهز للاستخدام! اختر الطريقة الأنسب لك وابدأ في إدارة صيدليتك بذكاء.** 📱💊✨

---

**بالتوفيق في إدارة صيدليتك! 🎉**
