{"version": 3, "names": ["xcodeEnvFile", "removeLastPathComponent", "pathString", "path", "dirname", "pathHasXcodeEnvFile", "xcodeEnvPath", "join", "fs", "existsSync", "pathDoesNotHaveXcodeEnvFile", "label", "description", "getDiagnostics", "_", "config", "iosFolderPath", "project", "ios", "sourceDir", "missingXcodeEnvFile", "findPodfilePaths", "some", "podfilePath", "needsToBeFixed", "e", "message", "runAutomaticFix", "loader", "stop", "templateXcodeEnv", "projectRoot", "root", "findProjectRoot", "templateIosPath", "resolveNodeModuleDir", "src", "copyFileAsync", "promisify", "copyFile", "map", "filter", "for<PERSON>ach", "destFile<PERSON>ath", "succeed", "fail"], "sources": ["../../../src/tools/healthchecks/xcodeEnv.ts"], "sourcesContent": ["import {findPodfilePaths} from '@react-native-community/cli-platform-apple';\nimport {\n  findProjectRoot,\n  resolveNodeModuleDir,\n} from '@react-native-community/cli-tools';\nimport fs from 'fs';\nimport path from 'path';\nimport {promisify} from 'util';\nimport {HealthCheckInterface} from '../../types';\n\nconst xcodeEnvFile = '.xcode.env';\n\nfunction removeLastPathComponent(pathString: string): string {\n  return path.dirname(pathString);\n}\n\nfunction pathHasXcodeEnvFile(pathString: string): boolean {\n  const xcodeEnvPath = path.join(pathString, xcodeEnvFile);\n  return fs.existsSync(xcodeEnvPath);\n}\n\nfunction pathDoesNotHaveXcodeEnvFile(pathString: string): boolean {\n  return !pathHasXcodeEnvFile(pathString);\n}\n\nexport default {\n  label: '.xcode.env',\n  description: 'File to customize Xcode environment',\n  getDiagnostics: async (_, config) => {\n    try {\n      const iosFolderPath = config?.project.ios?.sourceDir ?? '';\n\n      const missingXcodeEnvFile = findPodfilePaths(iosFolderPath).some(\n        (podfilePath) => {\n          return !pathHasXcodeEnvFile(\n            removeLastPathComponent(path.join(iosFolderPath, podfilePath)),\n          );\n        },\n      );\n\n      return {\n        needsToBeFixed: missingXcodeEnvFile,\n      };\n    } catch (e) {\n      return {\n        needsToBeFixed: (e as any).message,\n      };\n    }\n  },\n  runAutomaticFix: async ({loader, config}) => {\n    try {\n      loader.stop();\n      const templateXcodeEnv = '_xcode.env';\n      const projectRoot = config?.root ?? findProjectRoot();\n      const templateIosPath = resolveNodeModuleDir(\n        projectRoot,\n        'react-native/template/ios',\n      );\n      const src = path.join(templateIosPath, templateXcodeEnv);\n      const copyFileAsync = promisify(fs.copyFile);\n\n      const iosFolderPath = config?.project.ios?.sourceDir ?? '';\n\n      findPodfilePaths(iosFolderPath)\n        .map((podfilePath) =>\n          removeLastPathComponent(path.join(iosFolderPath, podfilePath)),\n        )\n        // avoid overriding existing .xcode.env\n        .filter(pathDoesNotHaveXcodeEnvFile)\n        .forEach(async (pathString: string) => {\n          const destFilePath = path.join(pathString, xcodeEnvFile);\n          await copyFileAsync(src, destFilePath);\n        });\n      loader.succeed('.xcode.env file have been created!');\n    } catch (e) {\n      loader.fail(e as any);\n    }\n  },\n} as HealthCheckInterface;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA+B;AAG/B,MAAMA,YAAY,GAAG,YAAY;AAEjC,SAASC,uBAAuB,CAACC,UAAkB,EAAU;EAC3D,OAAOC,eAAI,CAACC,OAAO,CAACF,UAAU,CAAC;AACjC;AAEA,SAASG,mBAAmB,CAACH,UAAkB,EAAW;EACxD,MAAMI,YAAY,GAAGH,eAAI,CAACI,IAAI,CAACL,UAAU,EAAEF,YAAY,CAAC;EACxD,OAAOQ,aAAE,CAACC,UAAU,CAACH,YAAY,CAAC;AACpC;AAEA,SAASI,2BAA2B,CAACR,UAAkB,EAAW;EAChE,OAAO,CAACG,mBAAmB,CAACH,UAAU,CAAC;AACzC;AAAC,eAEc;EACbS,KAAK,EAAE,YAAY;EACnBC,WAAW,EAAE,qCAAqC;EAClDC,cAAc,EAAE,OAAOC,CAAC,EAAEC,MAAM,KAAK;IACnC,IAAI;MAAA;MACF,MAAMC,aAAa,GAAG,CAAAD,MAAM,aAANA,MAAM,8CAANA,MAAM,CAAEE,OAAO,CAACC,GAAG,wDAAnB,oBAAqBC,SAAS,KAAI,EAAE;MAE1D,MAAMC,mBAAmB,GAAG,IAAAC,oCAAgB,EAACL,aAAa,CAAC,CAACM,IAAI,CAC7DC,WAAW,IAAK;QACf,OAAO,CAAClB,mBAAmB,CACzBJ,uBAAuB,CAACE,eAAI,CAACI,IAAI,CAACS,aAAa,EAAEO,WAAW,CAAC,CAAC,CAC/D;MACH,CAAC,CACF;MAED,OAAO;QACLC,cAAc,EAAEJ;MAClB,CAAC;IACH,CAAC,CAAC,OAAOK,CAAC,EAAE;MACV,OAAO;QACLD,cAAc,EAAGC,CAAC,CAASC;MAC7B,CAAC;IACH;EACF,CAAC;EACDC,eAAe,EAAE,OAAO;IAACC,MAAM;IAAEb;EAAM,CAAC,KAAK;IAC3C,IAAI;MAAA;MACFa,MAAM,CAACC,IAAI,EAAE;MACb,MAAMC,gBAAgB,GAAG,YAAY;MACrC,MAAMC,WAAW,GAAG,CAAAhB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEiB,IAAI,KAAI,IAAAC,2BAAe,GAAE;MACrD,MAAMC,eAAe,GAAG,IAAAC,gCAAoB,EAC1CJ,WAAW,EACX,2BAA2B,CAC5B;MACD,MAAMK,GAAG,GAAGjC,eAAI,CAACI,IAAI,CAAC2B,eAAe,EAAEJ,gBAAgB,CAAC;MACxD,MAAMO,aAAa,GAAG,IAAAC,iBAAS,EAAC9B,aAAE,CAAC+B,QAAQ,CAAC;MAE5C,MAAMvB,aAAa,GAAG,CAAAD,MAAM,aAANA,MAAM,+CAANA,MAAM,CAAEE,OAAO,CAACC,GAAG,yDAAnB,qBAAqBC,SAAS,KAAI,EAAE;MAE1D,IAAAE,oCAAgB,EAACL,aAAa,CAAC,CAC5BwB,GAAG,CAAEjB,WAAW,IACftB,uBAAuB,CAACE,eAAI,CAACI,IAAI,CAACS,aAAa,EAAEO,WAAW,CAAC,CAAC;MAEhE;MAAA,CACCkB,MAAM,CAAC/B,2BAA2B,CAAC,CACnCgC,OAAO,CAAC,MAAOxC,UAAkB,IAAK;QACrC,MAAMyC,YAAY,GAAGxC,eAAI,CAACI,IAAI,CAACL,UAAU,EAAEF,YAAY,CAAC;QACxD,MAAMqC,aAAa,CAACD,GAAG,EAAEO,YAAY,CAAC;MACxC,CAAC,CAAC;MACJf,MAAM,CAACgB,OAAO,CAAC,oCAAoC,CAAC;IACtD,CAAC,CAAC,OAAOnB,CAAC,EAAE;MACVG,MAAM,CAACiB,IAAI,CAACpB,CAAC,CAAQ;IACvB;EACF;AACF,CAAC;AAAA"}