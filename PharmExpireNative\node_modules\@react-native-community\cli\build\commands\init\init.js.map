{"version": 3, "names": ["DEFAULT_VERSION", "YARN_VERSION", "bumpYarnVersion", "root", "yarnVersion", "semver", "parse", "getYarnVersionIfAvailable", "setVersionArgs", "major", "minor", "executeCommand", "silent", "logger", "isVerbose", "e", "debug", "doesDirectoryExist", "dir", "fs", "existsSync", "getConflictsForDirectory", "directory", "readdirSync", "setProjectDirectory", "replaceDirectory", "directoryExists", "DirectoryAlreadyExistsError", "deleteDirectory", "conflicts", "length", "warnMessage", "chalk", "bold", "conflict", "warn", "replace", "prompt", "type", "name", "message", "removeSync", "mkdirSync", "recursive", "process", "chdir", "error", "CLIError", "cwd", "getTemplateName", "Object", "keys", "JSON", "readFileSync", "path", "join", "dependencies", "setEmptyHashForCachedDependencies", "projectName", "cacheManager", "set", "createHash", "update", "digest", "createFromTemplate", "shouldBumpYarnVersion", "templateUri", "pm", "projectTitle", "<PERSON><PERSON><PERSON><PERSON>", "packageName", "installCocoaPods", "yarnConfigOptions", "version", "env", "CI", "log", "banner", "undefined", "didInstallPods", "String", "packageManager", "userAgentPM", "userAgentPackageManager", "removeProjectCache", "projectDirectory", "loader", "<PERSON><PERSON><PERSON><PERSON>", "text", "templateSourceDir", "mkdtempSync", "os", "tmpdir", "start", "installTemplatePackage", "succeed", "templateName", "templateConfig", "getTemplateConfig", "copyTemplate", "templateDir", "changePlaceholderInTemplate", "placeholder<PERSON><PERSON>", "placeholder<PERSON><PERSON><PERSON>", "titlePlaceholder", "postInitScript", "info", "executePostInitScript", "installDependencies", "platform", "installPodsValue", "reactNativePath", "dirname", "require", "resolve", "paths", "runCodegen", "installPods", "installCocoapods", "reset", "dim", "Error", "exit", "PackageManager", "installAll", "checkPackageManagerAvailability", "getBunVersionIfAvailable", "getNpmVersionIfAvailable", "createProject", "options", "createTemplateUri", "title", "userAgent", "npm_config_user_agent", "startsWith", "initialize", "proj<PERSON><PERSON>", "validateProjectName", "updatedVersion", "npmResolveConcreteVersion", "platformName", "template", "semverVersion", "coerce", "gte", "TEMPLATE_COMMUNITY_REACT_NATIVE_VERSION", "TemplateAndVersionError", "directoryName", "relative", "projectFolder", "shouldCreateGitRepository", "isGitAvailable", "checkGitInstallation", "isFolderGitRepo", "checkIfFolderIsGitRepo", "skipGitInit", "createGitRepository", "printRunInstructions", "showPodsInstructions"], "sources": ["../../../src/commands/init/init.ts"], "sourcesContent": ["import os from 'os';\nimport path from 'path';\nimport fs, {readdirSync} from 'fs-extra';\nimport {validateProjectName} from './validate';\nimport chalk from 'chalk';\nimport printRunInstructions from './printRunInstructions';\nimport {\n  CLIError,\n  logger,\n  getLoader,\n  Loader,\n  cacheManager,\n  prompt,\n} from '@react-native-community/cli-tools';\nimport {installPods} from '@react-native-community/cli-platform-apple';\nimport {\n  installTemplatePackage,\n  getTemplateConfig,\n  copyTemplate,\n  executePostInitScript,\n} from './template';\nimport {changePlaceholderInTemplate} from './editTemplate';\nimport * as PackageManager from '../../tools/packageManager';\nimport banner from './banner';\nimport TemplateAndVersionError from './errors/TemplateAndVersionError';\nimport {getBunVersionIfAvailable} from '../../tools/bun';\nimport {\n  getNpmVersionIfAvailable,\n  npmResolveConcreteVersion,\n} from '../../tools/npm';\nimport {getYarnVersionIfAvailable} from '../../tools/yarn';\nimport {createHash} from 'crypto';\nimport {\n  createGitRepository,\n  checkGitInstallation,\n  checkIfFolderIsGitRepo,\n} from './git';\nimport semver from 'semver';\nimport {executeCommand} from '../../tools/executeCommand';\nimport DirectoryAlreadyExistsError from './errors/DirectoryAlreadyExistsError';\nimport {createTemplateUri} from './version';\nimport {TEMPLATE_COMMUNITY_REACT_NATIVE_VERSION} from './constants';\nimport type {Options} from './types';\nimport {runCodegen} from '@react-native-community/cli-config-apple';\n\nconst DEFAULT_VERSION = 'latest';\n\ninterface TemplateOptions {\n  projectName: string;\n  shouldBumpYarnVersion: boolean;\n  templateUri: string;\n  pm?: PackageManager.PackageManager;\n  directory: string;\n  projectTitle?: string;\n  skipInstall?: boolean;\n  packageName?: string;\n  installCocoaPods?: string | boolean;\n  version: string;\n  replaceDirectory?: string | boolean;\n  yarnConfigOptions?: Record<string, string>;\n}\n\ninterface TemplateReturnType {\n  didInstallPods?: boolean;\n  replaceDirectory?: string | boolean;\n}\n\n// Here we are defining explicit version of Yarn to be used in the new project because in some cases providing `3.x` don't work.\nconst YARN_VERSION = '3.6.4';\n\nconst bumpYarnVersion = async (root: string) => {\n  try {\n    let yarnVersion = semver.parse(getYarnVersionIfAvailable());\n\n    if (yarnVersion) {\n      // `yarn set` is unsupported until 1.22, however it's a alias (yarnpkg/yarn/pull/7862) calling `policies set-version`.\n      let setVersionArgs = ['set', 'version', YARN_VERSION];\n      if (yarnVersion.major === 1 && yarnVersion.minor < 22) {\n        setVersionArgs = ['policies', 'set-version', YARN_VERSION];\n      }\n      await executeCommand('yarn', setVersionArgs, {\n        root,\n        silent: !logger.isVerbose(),\n      });\n\n      // React Native doesn't support PnP, so we need to set nodeLinker to node-modules. Read more here: https://github.com/react-native-community/cli/issues/27#issuecomment-1772626767\n      await executeCommand(\n        'yarn',\n        ['config', 'set', 'nodeLinker', 'node-modules'],\n        {root, silent: !logger.isVerbose()},\n      );\n    }\n  } catch (e) {\n    logger.debug(e as string);\n  }\n};\n\nfunction doesDirectoryExist(dir: string) {\n  return fs.existsSync(dir);\n}\n\nfunction getConflictsForDirectory(directory: string) {\n  return readdirSync(directory);\n}\n\nasync function setProjectDirectory(\n  directory: string,\n  replaceDirectory: string,\n) {\n  const directoryExists = doesDirectoryExist(directory);\n\n  if (replaceDirectory === 'false' && directoryExists) {\n    throw new DirectoryAlreadyExistsError(directory);\n  }\n\n  let deleteDirectory = false;\n\n  if (replaceDirectory === 'true' && directoryExists) {\n    deleteDirectory = true;\n  } else if (directoryExists) {\n    const conflicts = getConflictsForDirectory(directory);\n\n    if (conflicts.length > 0) {\n      let warnMessage = `The directory ${chalk.bold(\n        directory,\n      )} contains files that will be overwritten:\\n`;\n\n      for (const conflict of conflicts) {\n        warnMessage += `   ${conflict}\\n`;\n      }\n\n      logger.warn(warnMessage);\n\n      const {replace} = await prompt({\n        type: 'confirm',\n        name: 'replace',\n        message: 'Do you want to replace existing files?',\n      });\n\n      deleteDirectory = replace;\n\n      if (!replace) {\n        throw new DirectoryAlreadyExistsError(directory);\n      }\n    }\n  }\n\n  try {\n    if (deleteDirectory) {\n      fs.removeSync(directory);\n    }\n\n    fs.mkdirSync(directory, {recursive: true});\n    process.chdir(directory);\n  } catch (error) {\n    throw new CLIError(\n      'Error occurred while trying to create project directory.',\n      error as Error,\n    );\n  }\n\n  return process.cwd();\n}\n\nfunction getTemplateName(cwd: string) {\n  // We use package manager to infer the name of the template module for us.\n  // That's why we get it from temporary package.json, where the name is the\n  // first and only dependency (hence 0).\n  const name = Object.keys(\n    JSON.parse(fs.readFileSync(path.join(cwd, './package.json'), 'utf8'))\n      .dependencies,\n  )[0];\n  return name;\n}\n\n//set cache to empty string to prevent installing cocoapods on freshly created project\nfunction setEmptyHashForCachedDependencies(projectName: string) {\n  cacheManager.set(\n    projectName,\n    'dependencies',\n    createHash('md5').update('').digest('hex'),\n  );\n}\n\nasync function createFromTemplate({\n  projectName,\n  shouldBumpYarnVersion,\n  templateUri,\n  pm,\n  directory,\n  projectTitle,\n  skipInstall,\n  packageName,\n  installCocoaPods,\n  replaceDirectory,\n  yarnConfigOptions,\n  version,\n}: TemplateOptions): Promise<TemplateReturnType> {\n  logger.debug('Initializing new project');\n  // Only print out the banner if we're not in a CI\n  if (!process.env.CI) {\n    logger.log(banner(version !== DEFAULT_VERSION ? version : undefined));\n  }\n  let didInstallPods = String(installCocoaPods) === 'true';\n  let packageManager = pm;\n\n  if (pm) {\n    packageManager = pm;\n  } else {\n    const userAgentPM = userAgentPackageManager();\n    // if possible, use the package manager from the user agent. Otherwise fallback to default (npm)\n    packageManager = userAgentPM || 'npm';\n  }\n\n  // if the project with the name already has cache, remove the cache to avoid problems with pods installation\n  cacheManager.removeProjectCache(projectName);\n\n  const projectDirectory = await setProjectDirectory(\n    directory,\n    String(replaceDirectory),\n  );\n\n  const loader = getLoader({text: 'Downloading template'});\n  const templateSourceDir = fs.mkdtempSync(\n    path.join(os.tmpdir(), 'rncli-init-template-'),\n  );\n\n  try {\n    loader.start();\n\n    await installTemplatePackage(\n      templateUri,\n      templateSourceDir,\n      packageManager,\n      yarnConfigOptions,\n    );\n\n    loader.succeed();\n    loader.start('Copying template');\n\n    const templateName = getTemplateName(templateSourceDir);\n    const templateConfig = getTemplateConfig(templateName, templateSourceDir);\n    await copyTemplate(\n      templateName,\n      templateConfig.templateDir,\n      templateSourceDir,\n    );\n\n    loader.succeed();\n    loader.start('Processing template');\n\n    await changePlaceholderInTemplate({\n      projectName,\n      projectTitle,\n      placeholderName: templateConfig.placeholderName,\n      placeholderTitle: templateConfig.titlePlaceholder,\n      packageName,\n    });\n\n    if (packageManager === 'yarn' && shouldBumpYarnVersion) {\n      await bumpYarnVersion(projectDirectory);\n    }\n\n    loader.succeed();\n    const {postInitScript} = templateConfig;\n    if (postInitScript) {\n      loader.info('Executing post init script ');\n      await executePostInitScript(\n        templateName,\n        postInitScript,\n        templateSourceDir,\n      );\n    }\n\n    if (!skipInstall) {\n      await installDependencies({\n        packageManager,\n        loader,\n        root: projectDirectory,\n      });\n\n      if (process.platform === 'darwin') {\n        const installPodsValue = String(installCocoaPods);\n        const reactNativePath = path.dirname(\n          require.resolve('react-native', {paths: [projectDirectory]}),\n        );\n\n        try {\n          if (installPodsValue === 'true') {\n            didInstallPods = true;\n            await runCodegen({\n              root: projectDirectory,\n              platform: 'ios',\n              reactNativePath,\n            });\n            await installPods(loader, {});\n            loader.succeed();\n            setEmptyHashForCachedDependencies(projectName);\n          } else if (installPodsValue === 'undefined') {\n            const {installCocoapods} = await prompt({\n              type: 'confirm',\n              name: 'installCocoapods',\n              message: `Do you want to install CocoaPods now? ${chalk.reset.dim(\n                'Needed for running iOS project',\n              )}`,\n            });\n            didInstallPods = installCocoapods;\n\n            if (installCocoapods) {\n              await runCodegen({\n                root: projectDirectory,\n                platform: 'ios',\n                reactNativePath,\n              });\n              await installPods(loader, {});\n              loader.succeed();\n              setEmptyHashForCachedDependencies(projectName);\n            }\n          }\n        } catch (error) {\n          logger.error(\n            `Installing Cocoapods failed. This doesn't affect project initialization and you can safely proceed. However, you will need to install Cocoapods manually when running iOS, follow additional steps in \"Run instructions for iOS\" section.\\n\\nError: ${\n              (error as Error).message as string\n            }\\n`,\n          );\n        }\n      }\n    } else {\n      didInstallPods = false;\n      loader.succeed('Dependencies installation skipped');\n    }\n\n    fs.removeSync(templateSourceDir);\n  } catch (e) {\n    logger.log('\\n');\n    if (e instanceof CLIError) {\n      logger.error(e.message);\n    } else if (e instanceof Error) {\n      logger.error(`An unexpected error occurred: ${e.message}.`);\n    }\n    didInstallPods = false;\n    logger.debug(e as any);\n    fs.removeSync(templateSourceDir);\n    process.exit(1);\n  }\n\n  return {didInstallPods};\n}\n\nasync function installDependencies({\n  packageManager,\n  loader,\n  root,\n}: {\n  packageManager: PackageManager.PackageManager;\n  loader: Loader;\n  root: string;\n}) {\n  loader.start('Installing dependencies');\n\n  await PackageManager.installAll({\n    packageManager,\n    silent: true,\n    root,\n  });\n\n  loader.succeed();\n}\n\nfunction checkPackageManagerAvailability(\n  packageManager: PackageManager.PackageManager,\n) {\n  if (packageManager === 'bun') {\n    return getBunVersionIfAvailable();\n  } else if (packageManager === 'npm') {\n    return getNpmVersionIfAvailable();\n  } else if (packageManager === 'yarn') {\n    return getYarnVersionIfAvailable();\n  }\n\n  return false;\n}\n\nasync function createProject(\n  projectName: string,\n  directory: string,\n  version: string,\n  shouldBumpYarnVersion: boolean,\n  options: Options,\n): Promise<TemplateReturnType> {\n  // Handle these cases (when community template is published and react-native >= 0.75\n  //\n  // +==================================================================+==========+===================+\n  // | Arguments                                                        | Template |   React Native    |\n  // +==================================================================+==========+===================+\n  // | <None>                                                           | 0.74.x   | 0.74.5 (latest)   |\n  // +------------------------------------------------------------------+----------+-------------------+\n  // | --version next                                                   | 0.75.x   | 0.75.0-rc.1 (next)|\n  // +------------------------------------------------------------------+----------+-------------------+\n  // | --version 0.75.0                                                 | 0.75.x   | 0.75.0            |\n  // +------------------------------------------------------------------+----------+-------------------+\n  // | --template @react-native-community/template@0.75.1               | 0.75.1   | latest            |\n  // +------------------------------------------------------------------+----------+-------------------+\n  // | --template @react-native-community/template@0.75.1 --version 0.75| 0.75.1   | 0.75.x            |\n  // +------------------------------------------------------------------+----------+-------------------+\n  //\n  // 1. If you specify `--version 0.75.0` and `@react-native-community/template@0.75.0` is *NOT*\n  // published, then `init` will exit and suggest explicitly using the `--template` argument.\n  //\n  // 2. `--template` will always win over `--version` for the template.\n  //\n  // 3. For version < 0.75, the template ships with react-native.\n  const templateUri = await createTemplateUri(options, version);\n\n  logger.debug(`Template: '${templateUri}'`);\n\n  return createFromTemplate({\n    projectName,\n    shouldBumpYarnVersion,\n    templateUri,\n    pm: options.pm,\n    directory,\n    projectTitle: options.title,\n    skipInstall: options.skipInstall,\n    packageName: options.packageName,\n    installCocoaPods: options.installPods,\n    version,\n    replaceDirectory: options.replaceDirectory,\n    yarnConfigOptions: options.yarnConfigOptions,\n  });\n}\n\nfunction userAgentPackageManager() {\n  const userAgent = process.env.npm_config_user_agent;\n\n  if (userAgent && userAgent.startsWith('bun')) {\n    return 'bun';\n  }\n\n  return null;\n}\n\nexport default (async function initialize(\n  [projectName]: Array<string>,\n  options: Options,\n) {\n  if (!projectName) {\n    const {projName} = await prompt({\n      type: 'text',\n      name: 'projName',\n      message: 'How would you like to name the app?',\n    });\n    projectName = projName;\n  }\n\n  validateProjectName(projectName);\n\n  let version = options.version ?? DEFAULT_VERSION;\n\n  try {\n    const updatedVersion = await npmResolveConcreteVersion(\n      options.platformName ?? 'react-native',\n      version,\n    );\n    logger.debug(`Mapped: ${version} -> ${updatedVersion}`);\n    version = updatedVersion;\n  } catch (e) {\n    logger.debug(\n      `Failed to get concrete version from '${version}': `,\n      e as any,\n    );\n  }\n\n  // From 0.75 it actually is useful to be able to specify both the template and react-native version.\n  // This should only be used by people who know what they're doing.\n  if (!!options.template && !!options.version) {\n    // 0.75.0-nightly-20240618-5df5ed1a8' -> 0.75.0\n    // 0.75.0-rc.1 -> 0.75.0\n    const semverVersion = semver.coerce(version)?.version ?? version;\n    if (semver.gte(semverVersion, TEMPLATE_COMMUNITY_REACT_NATIVE_VERSION)) {\n      logger.warn(\n        `Use ${chalk.bold('--template')} and ${chalk.bold(\n          '--version',\n        )} only if you know what you're doing. Here be dragons 🐉.`,\n      );\n    } else {\n      throw new TemplateAndVersionError(options.template);\n    }\n  }\n\n  const root = process.cwd();\n\n  const directoryName = path.relative(root, options.directory || projectName);\n  const projectFolder = path.join(root, directoryName);\n\n  if (options.pm && !checkPackageManagerAvailability(options.pm)) {\n    logger.error(\n      'Seems like the package manager you want to use is not installed. Please install it or choose another package manager.',\n    );\n    return;\n  }\n\n  let shouldBumpYarnVersion = true;\n  let shouldCreateGitRepository = false;\n\n  const isGitAvailable = await checkGitInstallation();\n\n  if (isGitAvailable) {\n    const isFolderGitRepo = await checkIfFolderIsGitRepo(projectFolder);\n\n    if (isFolderGitRepo) {\n      shouldBumpYarnVersion = false;\n    } else {\n      shouldCreateGitRepository = true; // Initialize git repo after creating project\n    }\n  } else {\n    logger.warn(\n      'Git is not installed on your system. This might cause some features to work incorrectly.',\n    );\n  }\n\n  const {didInstallPods} = await createProject(\n    projectName,\n    directoryName,\n    version,\n    shouldBumpYarnVersion,\n    options,\n  );\n\n  if (shouldCreateGitRepository && !options.skipGitInit) {\n    await createGitRepository(projectFolder);\n  }\n\n  printRunInstructions(projectFolder, projectName, {\n    showPodsInstructions: !didInstallPods,\n  });\n});\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAKA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;AACA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAoE;AAAA;AAAA;AAEpE,MAAMA,eAAe,GAAG,QAAQ;AAsBhC;AACA,MAAMC,YAAY,GAAG,OAAO;AAE5B,MAAMC,eAAe,GAAG,MAAOC,IAAY,IAAK;EAC9C,IAAI;IACF,IAAIC,WAAW,GAAGC,iBAAM,CAACC,KAAK,CAAC,IAAAC,+BAAyB,GAAE,CAAC;IAE3D,IAAIH,WAAW,EAAE;MACf;MACA,IAAII,cAAc,GAAG,CAAC,KAAK,EAAE,SAAS,EAAEP,YAAY,CAAC;MACrD,IAAIG,WAAW,CAACK,KAAK,KAAK,CAAC,IAAIL,WAAW,CAACM,KAAK,GAAG,EAAE,EAAE;QACrDF,cAAc,GAAG,CAAC,UAAU,EAAE,aAAa,EAAEP,YAAY,CAAC;MAC5D;MACA,MAAM,IAAAU,8BAAc,EAAC,MAAM,EAAEH,cAAc,EAAE;QAC3CL,IAAI;QACJS,MAAM,EAAE,CAACC,kBAAM,CAACC,SAAS;MAC3B,CAAC,CAAC;;MAEF;MACA,MAAM,IAAAH,8BAAc,EAClB,MAAM,EACN,CAAC,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,cAAc,CAAC,EAC/C;QAACR,IAAI;QAAES,MAAM,EAAE,CAACC,kBAAM,CAACC,SAAS;MAAE,CAAC,CACpC;IACH;EACF,CAAC,CAAC,OAAOC,CAAC,EAAE;IACVF,kBAAM,CAACG,KAAK,CAACD,CAAC,CAAW;EAC3B;AACF,CAAC;AAED,SAASE,kBAAkB,CAACC,GAAW,EAAE;EACvC,OAAOC,kBAAE,CAACC,UAAU,CAACF,GAAG,CAAC;AAC3B;AAEA,SAASG,wBAAwB,CAACC,SAAiB,EAAE;EACnD,OAAO,IAAAC,sBAAW,EAACD,SAAS,CAAC;AAC/B;AAEA,eAAeE,mBAAmB,CAChCF,SAAiB,EACjBG,gBAAwB,EACxB;EACA,MAAMC,eAAe,GAAGT,kBAAkB,CAACK,SAAS,CAAC;EAErD,IAAIG,gBAAgB,KAAK,OAAO,IAAIC,eAAe,EAAE;IACnD,MAAM,IAAIC,oCAA2B,CAACL,SAAS,CAAC;EAClD;EAEA,IAAIM,eAAe,GAAG,KAAK;EAE3B,IAAIH,gBAAgB,KAAK,MAAM,IAAIC,eAAe,EAAE;IAClDE,eAAe,GAAG,IAAI;EACxB,CAAC,MAAM,IAAIF,eAAe,EAAE;IAC1B,MAAMG,SAAS,GAAGR,wBAAwB,CAACC,SAAS,CAAC;IAErD,IAAIO,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;MACxB,IAAIC,WAAW,GAAI,iBAAgBC,gBAAK,CAACC,IAAI,CAC3CX,SAAS,CACT,6CAA4C;MAE9C,KAAK,MAAMY,QAAQ,IAAIL,SAAS,EAAE;QAChCE,WAAW,IAAK,MAAKG,QAAS,IAAG;MACnC;MAEArB,kBAAM,CAACsB,IAAI,CAACJ,WAAW,CAAC;MAExB,MAAM;QAACK;MAAO,CAAC,GAAG,MAAM,IAAAC,kBAAM,EAAC;QAC7BC,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE,SAAS;QACfC,OAAO,EAAE;MACX,CAAC,CAAC;MAEFZ,eAAe,GAAGQ,OAAO;MAEzB,IAAI,CAACA,OAAO,EAAE;QACZ,MAAM,IAAIT,oCAA2B,CAACL,SAAS,CAAC;MAClD;IACF;EACF;EAEA,IAAI;IACF,IAAIM,eAAe,EAAE;MACnBT,kBAAE,CAACsB,UAAU,CAACnB,SAAS,CAAC;IAC1B;IAEAH,kBAAE,CAACuB,SAAS,CAACpB,SAAS,EAAE;MAACqB,SAAS,EAAE;IAAI,CAAC,CAAC;IAC1CC,OAAO,CAACC,KAAK,CAACvB,SAAS,CAAC;EAC1B,CAAC,CAAC,OAAOwB,KAAK,EAAE;IACd,MAAM,KAAIC,oBAAQ,EAChB,0DAA0D,EAC1DD,KAAK,CACN;EACH;EAEA,OAAOF,OAAO,CAACI,GAAG,EAAE;AACtB;AAEA,SAASC,eAAe,CAACD,GAAW,EAAE;EACpC;EACA;EACA;EACA,MAAMT,IAAI,GAAGW,MAAM,CAACC,IAAI,CACtBC,IAAI,CAAC9C,KAAK,CAACa,kBAAE,CAACkC,YAAY,CAACC,eAAI,CAACC,IAAI,CAACP,GAAG,EAAE,gBAAgB,CAAC,EAAE,MAAM,CAAC,CAAC,CAClEQ,YAAY,CAChB,CAAC,CAAC,CAAC;EACJ,OAAOjB,IAAI;AACb;;AAEA;AACA,SAASkB,iCAAiC,CAACC,WAAmB,EAAE;EAC9DC,wBAAY,CAACC,GAAG,CACdF,WAAW,EACX,cAAc,EACd,IAAAG,oBAAU,EAAC,KAAK,CAAC,CAACC,MAAM,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,KAAK,CAAC,CAC3C;AACH;AAEA,eAAeC,kBAAkB,CAAC;EAChCN,WAAW;EACXO,qBAAqB;EACrBC,WAAW;EACXC,EAAE;EACF7C,SAAS;EACT8C,YAAY;EACZC,WAAW;EACXC,WAAW;EACXC,gBAAgB;EAChB9C,gBAAgB;EAChB+C,iBAAiB;EACjBC;AACe,CAAC,EAA+B;EAC/C5D,kBAAM,CAACG,KAAK,CAAC,0BAA0B,CAAC;EACxC;EACA,IAAI,CAAC4B,OAAO,CAAC8B,GAAG,CAACC,EAAE,EAAE;IACnB9D,kBAAM,CAAC+D,GAAG,CAAC,IAAAC,eAAM,EAACJ,OAAO,KAAKzE,eAAe,GAAGyE,OAAO,GAAGK,SAAS,CAAC,CAAC;EACvE;EACA,IAAIC,cAAc,GAAGC,MAAM,CAACT,gBAAgB,CAAC,KAAK,MAAM;EACxD,IAAIU,cAAc,GAAGd,EAAE;EAEvB,IAAIA,EAAE,EAAE;IACNc,cAAc,GAAGd,EAAE;EACrB,CAAC,MAAM;IACL,MAAMe,WAAW,GAAGC,uBAAuB,EAAE;IAC7C;IACAF,cAAc,GAAGC,WAAW,IAAI,KAAK;EACvC;;EAEA;EACAvB,wBAAY,CAACyB,kBAAkB,CAAC1B,WAAW,CAAC;EAE5C,MAAM2B,gBAAgB,GAAG,MAAM7D,mBAAmB,CAChDF,SAAS,EACT0D,MAAM,CAACvD,gBAAgB,CAAC,CACzB;EAED,MAAM6D,MAAM,GAAG,IAAAC,qBAAS,EAAC;IAACC,IAAI,EAAE;EAAsB,CAAC,CAAC;EACxD,MAAMC,iBAAiB,GAAGtE,kBAAE,CAACuE,WAAW,CACtCpC,eAAI,CAACC,IAAI,CAACoC,aAAE,CAACC,MAAM,EAAE,EAAE,sBAAsB,CAAC,CAC/C;EAED,IAAI;IACFN,MAAM,CAACO,KAAK,EAAE;IAEd,MAAM,IAAAC,gCAAsB,EAC1B5B,WAAW,EACXuB,iBAAiB,EACjBR,cAAc,EACdT,iBAAiB,CAClB;IAEDc,MAAM,CAACS,OAAO,EAAE;IAChBT,MAAM,CAACO,KAAK,CAAC,kBAAkB,CAAC;IAEhC,MAAMG,YAAY,GAAG/C,eAAe,CAACwC,iBAAiB,CAAC;IACvD,MAAMQ,cAAc,GAAG,IAAAC,2BAAiB,EAACF,YAAY,EAAEP,iBAAiB,CAAC;IACzE,MAAM,IAAAU,sBAAY,EAChBH,YAAY,EACZC,cAAc,CAACG,WAAW,EAC1BX,iBAAiB,CAClB;IAEDH,MAAM,CAACS,OAAO,EAAE;IAChBT,MAAM,CAACO,KAAK,CAAC,qBAAqB,CAAC;IAEnC,MAAM,IAAAQ,yCAA2B,EAAC;MAChC3C,WAAW;MACXU,YAAY;MACZkC,eAAe,EAAEL,cAAc,CAACK,eAAe;MAC/CC,gBAAgB,EAAEN,cAAc,CAACO,gBAAgB;MACjDlC;IACF,CAAC,CAAC;IAEF,IAAIW,cAAc,KAAK,MAAM,IAAIhB,qBAAqB,EAAE;MACtD,MAAM/D,eAAe,CAACmF,gBAAgB,CAAC;IACzC;IAEAC,MAAM,CAACS,OAAO,EAAE;IAChB,MAAM;MAACU;IAAc,CAAC,GAAGR,cAAc;IACvC,IAAIQ,cAAc,EAAE;MAClBnB,MAAM,CAACoB,IAAI,CAAC,6BAA6B,CAAC;MAC1C,MAAM,IAAAC,+BAAqB,EACzBX,YAAY,EACZS,cAAc,EACdhB,iBAAiB,CAClB;IACH;IAEA,IAAI,CAACpB,WAAW,EAAE;MAChB,MAAMuC,mBAAmB,CAAC;QACxB3B,cAAc;QACdK,MAAM;QACNnF,IAAI,EAAEkF;MACR,CAAC,CAAC;MAEF,IAAIzC,OAAO,CAACiE,QAAQ,KAAK,QAAQ,EAAE;QACjC,MAAMC,gBAAgB,GAAG9B,MAAM,CAACT,gBAAgB,CAAC;QACjD,MAAMwC,eAAe,GAAGzD,eAAI,CAAC0D,OAAO,CAClCC,OAAO,CAACC,OAAO,CAAC,cAAc,EAAE;UAACC,KAAK,EAAE,CAAC9B,gBAAgB;QAAC,CAAC,CAAC,CAC7D;QAED,IAAI;UACF,IAAIyB,gBAAgB,KAAK,MAAM,EAAE;YAC/B/B,cAAc,GAAG,IAAI;YACrB,MAAM,IAAAqC,4BAAU,EAAC;cACfjH,IAAI,EAAEkF,gBAAgB;cACtBwB,QAAQ,EAAE,KAAK;cACfE;YACF,CAAC,CAAC;YACF,MAAM,IAAAM,+BAAW,EAAC/B,MAAM,EAAE,CAAC,CAAC,CAAC;YAC7BA,MAAM,CAACS,OAAO,EAAE;YAChBtC,iCAAiC,CAACC,WAAW,CAAC;UAChD,CAAC,MAAM,IAAIoD,gBAAgB,KAAK,WAAW,EAAE;YAC3C,MAAM;cAACQ;YAAgB,CAAC,GAAG,MAAM,IAAAjF,kBAAM,EAAC;cACtCC,IAAI,EAAE,SAAS;cACfC,IAAI,EAAE,kBAAkB;cACxBC,OAAO,EAAG,yCAAwCR,gBAAK,CAACuF,KAAK,CAACC,GAAG,CAC/D,gCAAgC,CAChC;YACJ,CAAC,CAAC;YACFzC,cAAc,GAAGuC,gBAAgB;YAEjC,IAAIA,gBAAgB,EAAE;cACpB,MAAM,IAAAF,4BAAU,EAAC;gBACfjH,IAAI,EAAEkF,gBAAgB;gBACtBwB,QAAQ,EAAE,KAAK;gBACfE;cACF,CAAC,CAAC;cACF,MAAM,IAAAM,+BAAW,EAAC/B,MAAM,EAAE,CAAC,CAAC,CAAC;cAC7BA,MAAM,CAACS,OAAO,EAAE;cAChBtC,iCAAiC,CAACC,WAAW,CAAC;YAChD;UACF;QACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;UACdjC,kBAAM,CAACiC,KAAK,CACT,uPACEA,KAAK,CAAWN,OAClB,IAAG,CACL;QACH;MACF;IACF,CAAC,MAAM;MACLuC,cAAc,GAAG,KAAK;MACtBO,MAAM,CAACS,OAAO,CAAC,mCAAmC,CAAC;IACrD;IAEA5E,kBAAE,CAACsB,UAAU,CAACgD,iBAAiB,CAAC;EAClC,CAAC,CAAC,OAAO1E,CAAC,EAAE;IACVF,kBAAM,CAAC+D,GAAG,CAAC,IAAI,CAAC;IAChB,IAAI7D,CAAC,YAAYgC,oBAAQ,EAAE;MACzBlC,kBAAM,CAACiC,KAAK,CAAC/B,CAAC,CAACyB,OAAO,CAAC;IACzB,CAAC,MAAM,IAAIzB,CAAC,YAAY0G,KAAK,EAAE;MAC7B5G,kBAAM,CAACiC,KAAK,CAAE,iCAAgC/B,CAAC,CAACyB,OAAQ,GAAE,CAAC;IAC7D;IACAuC,cAAc,GAAG,KAAK;IACtBlE,kBAAM,CAACG,KAAK,CAACD,CAAC,CAAQ;IACtBI,kBAAE,CAACsB,UAAU,CAACgD,iBAAiB,CAAC;IAChC7C,OAAO,CAAC8E,IAAI,CAAC,CAAC,CAAC;EACjB;EAEA,OAAO;IAAC3C;EAAc,CAAC;AACzB;AAEA,eAAe6B,mBAAmB,CAAC;EACjC3B,cAAc;EACdK,MAAM;EACNnF;AAKF,CAAC,EAAE;EACDmF,MAAM,CAACO,KAAK,CAAC,yBAAyB,CAAC;EAEvC,MAAM8B,cAAc,CAACC,UAAU,CAAC;IAC9B3C,cAAc;IACdrE,MAAM,EAAE,IAAI;IACZT;EACF,CAAC,CAAC;EAEFmF,MAAM,CAACS,OAAO,EAAE;AAClB;AAEA,SAAS8B,+BAA+B,CACtC5C,cAA6C,EAC7C;EACA,IAAIA,cAAc,KAAK,KAAK,EAAE;IAC5B,OAAO,IAAA6C,6BAAwB,GAAE;EACnC,CAAC,MAAM,IAAI7C,cAAc,KAAK,KAAK,EAAE;IACnC,OAAO,IAAA8C,6BAAwB,GAAE;EACnC,CAAC,MAAM,IAAI9C,cAAc,KAAK,MAAM,EAAE;IACpC,OAAO,IAAA1E,+BAAyB,GAAE;EACpC;EAEA,OAAO,KAAK;AACd;AAEA,eAAeyH,aAAa,CAC1BtE,WAAmB,EACnBpC,SAAiB,EACjBmD,OAAe,EACfR,qBAA8B,EAC9BgE,OAAgB,EACa;EAC7B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAM/D,WAAW,GAAG,MAAM,IAAAgE,0BAAiB,EAACD,OAAO,EAAExD,OAAO,CAAC;EAE7D5D,kBAAM,CAACG,KAAK,CAAE,cAAakD,WAAY,GAAE,CAAC;EAE1C,OAAOF,kBAAkB,CAAC;IACxBN,WAAW;IACXO,qBAAqB;IACrBC,WAAW;IACXC,EAAE,EAAE8D,OAAO,CAAC9D,EAAE;IACd7C,SAAS;IACT8C,YAAY,EAAE6D,OAAO,CAACE,KAAK;IAC3B9D,WAAW,EAAE4D,OAAO,CAAC5D,WAAW;IAChCC,WAAW,EAAE2D,OAAO,CAAC3D,WAAW;IAChCC,gBAAgB,EAAE0D,OAAO,CAACZ,WAAW;IACrC5C,OAAO;IACPhD,gBAAgB,EAAEwG,OAAO,CAACxG,gBAAgB;IAC1C+C,iBAAiB,EAAEyD,OAAO,CAACzD;EAC7B,CAAC,CAAC;AACJ;AAEA,SAASW,uBAAuB,GAAG;EACjC,MAAMiD,SAAS,GAAGxF,OAAO,CAAC8B,GAAG,CAAC2D,qBAAqB;EAEnD,IAAID,SAAS,IAAIA,SAAS,CAACE,UAAU,CAAC,KAAK,CAAC,EAAE;IAC5C,OAAO,KAAK;EACd;EAEA,OAAO,IAAI;AACb;AAAC,IAE8BC,UAAU,GAAzB,eAAeA,UAAU,CACvC,CAAC7E,WAAW,CAAgB,EAC5BuE,OAAgB,EAChB;EACA,IAAI,CAACvE,WAAW,EAAE;IAChB,MAAM;MAAC8E;IAAQ,CAAC,GAAG,MAAM,IAAAnG,kBAAM,EAAC;MAC9BC,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE;IACX,CAAC,CAAC;IACFkB,WAAW,GAAG8E,QAAQ;EACxB;EAEA,IAAAC,6BAAmB,EAAC/E,WAAW,CAAC;EAEhC,IAAIe,OAAO,GAAGwD,OAAO,CAACxD,OAAO,IAAIzE,eAAe;EAEhD,IAAI;IACF,MAAM0I,cAAc,GAAG,MAAM,IAAAC,8BAAyB,EACpDV,OAAO,CAACW,YAAY,IAAI,cAAc,EACtCnE,OAAO,CACR;IACD5D,kBAAM,CAACG,KAAK,CAAE,WAAUyD,OAAQ,OAAMiE,cAAe,EAAC,CAAC;IACvDjE,OAAO,GAAGiE,cAAc;EAC1B,CAAC,CAAC,OAAO3H,CAAC,EAAE;IACVF,kBAAM,CAACG,KAAK,CACT,wCAAuCyD,OAAQ,KAAI,EACpD1D,CAAC,CACF;EACH;;EAEA;EACA;EACA,IAAI,CAAC,CAACkH,OAAO,CAACY,QAAQ,IAAI,CAAC,CAACZ,OAAO,CAACxD,OAAO,EAAE;IAAA;IAC3C;IACA;IACA,MAAMqE,aAAa,GAAG,mBAAAzI,iBAAM,CAAC0I,MAAM,CAACtE,OAAO,CAAC,mDAAtB,eAAwBA,OAAO,KAAIA,OAAO;IAChE,IAAIpE,iBAAM,CAAC2I,GAAG,CAACF,aAAa,EAAEG,kDAAuC,CAAC,EAAE;MACtEpI,kBAAM,CAACsB,IAAI,CACR,OAAMH,gBAAK,CAACC,IAAI,CAAC,YAAY,CAAE,QAAOD,gBAAK,CAACC,IAAI,CAC/C,WAAW,CACX,0DAAyD,CAC5D;IACH,CAAC,MAAM;MACL,MAAM,IAAIiH,gCAAuB,CAACjB,OAAO,CAACY,QAAQ,CAAC;IACrD;EACF;EAEA,MAAM1I,IAAI,GAAGyC,OAAO,CAACI,GAAG,EAAE;EAE1B,MAAMmG,aAAa,GAAG7F,eAAI,CAAC8F,QAAQ,CAACjJ,IAAI,EAAE8H,OAAO,CAAC3G,SAAS,IAAIoC,WAAW,CAAC;EAC3E,MAAM2F,aAAa,GAAG/F,eAAI,CAACC,IAAI,CAACpD,IAAI,EAAEgJ,aAAa,CAAC;EAEpD,IAAIlB,OAAO,CAAC9D,EAAE,IAAI,CAAC0D,+BAA+B,CAACI,OAAO,CAAC9D,EAAE,CAAC,EAAE;IAC9DtD,kBAAM,CAACiC,KAAK,CACV,uHAAuH,CACxH;IACD;EACF;EAEA,IAAImB,qBAAqB,GAAG,IAAI;EAChC,IAAIqF,yBAAyB,GAAG,KAAK;EAErC,MAAMC,cAAc,GAAG,MAAM,IAAAC,yBAAoB,GAAE;EAEnD,IAAID,cAAc,EAAE;IAClB,MAAME,eAAe,GAAG,MAAM,IAAAC,2BAAsB,EAACL,aAAa,CAAC;IAEnE,IAAII,eAAe,EAAE;MACnBxF,qBAAqB,GAAG,KAAK;IAC/B,CAAC,MAAM;MACLqF,yBAAyB,GAAG,IAAI,CAAC,CAAC;IACpC;EACF,CAAC,MAAM;IACLzI,kBAAM,CAACsB,IAAI,CACT,0FAA0F,CAC3F;EACH;EAEA,MAAM;IAAC4C;EAAc,CAAC,GAAG,MAAMiD,aAAa,CAC1CtE,WAAW,EACXyF,aAAa,EACb1E,OAAO,EACPR,qBAAqB,EACrBgE,OAAO,CACR;EAED,IAAIqB,yBAAyB,IAAI,CAACrB,OAAO,CAAC0B,WAAW,EAAE;IACrD,MAAM,IAAAC,wBAAmB,EAACP,aAAa,CAAC;EAC1C;EAEA,IAAAQ,6BAAoB,EAACR,aAAa,EAAE3F,WAAW,EAAE;IAC/CoG,oBAAoB,EAAE,CAAC/E;EACzB,CAAC,CAAC;AACJ,CAAC;AAAA"}