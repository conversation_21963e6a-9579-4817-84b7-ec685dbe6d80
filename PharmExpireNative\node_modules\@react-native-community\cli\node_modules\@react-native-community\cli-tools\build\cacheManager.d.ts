type CacheKey = 'eTag' | 'lastChecked' | 'latestVersion' | 'dependencies' | 'podfile' | 'podfileLock' | 'lastUsedIOSDeviceId';
/**
 * Returns the path string of `$HOME/.react-native-cli`.
 *
 * In case it doesn't exist, it will be created.
 */
declare function getCacheRootPath(): string;
declare function removeProjectCache(name: string): void;
declare function get(name: string, key: <PERSON>ache<PERSON>ey): string | undefined;
declare function set(name: string, key: CacheKey, value: string): void;
declare const _default: {
    get: typeof get;
    set: typeof set;
    removeProjectCache: typeof removeProjectCache;
    getCacheRootPath: typeof getCacheRootPath;
};
export default _default;
//# sourceMappingURL=cacheManager.d.ts.map