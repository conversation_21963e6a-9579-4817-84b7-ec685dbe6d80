{"version": 3, "names": ["pick<PERSON><PERSON><PERSON>", "obj", "keys", "names", "finalNames", "results", "pickedV<PERSON>ue", "index", "length", "value", "toString", "undefined", "findPackageDependencyDir", "ref", "options", "pkgName", "path", "join", "scope", "name", "packageDir", "findUp", "sync", "type", "resolveSymlinks", "fs", "lstatSync", "isSymbolicLink", "resolve", "dirname", "readlinkSync"], "sources": ["../src/findPackageDependencyDir.ts"], "sourcesContent": ["/**\n * Source vendored from:\n * https://github.com/microsoft/rnx-kit/blob/f37adca5161eba66fc27de25d48f72973fff9e8e/packages/tools-node/src/package.ts#L213-L234\n */\nimport findUp from 'find-up';\nimport * as fs from 'fs';\nimport * as path from 'path';\n\n/**\n * Pick the value for each `key` property from `obj` and return each one in a new object.\n * If `names` are given, use them in the new object, instead of `keys`.\n *\n * If any `key` was not found or its value was `undefined`, nothing will be picked for that key.\n *\n * @param obj Object to pick from\n * @param keys Keys to pick\n * @param names Optional names to use in the output object\n * @returns A new object containing a each `name` property and the picked value, or `undefined` if no keys were picked.\n */\nexport function pickValues<T>(\n  obj: T,\n  keys: (keyof T)[],\n  names?: string[],\n): Record<string, unknown> | undefined {\n  const finalNames = names ?? keys;\n  const results: Record<string, unknown> = {};\n\n  let pickedValue = false;\n  for (let index = 0; index < keys.length; ++index) {\n    const value = obj[keys[index]];\n    if (typeof value !== 'undefined') {\n      results[finalNames[index].toString()] = value;\n      pickedValue = true;\n    }\n  }\n\n  return pickedValue ? results : undefined;\n}\n\n/**\n * Components of a package reference.\n */\nexport type PackageRef = {\n  scope?: string;\n  name: string;\n};\n\n/**\n * Options which control how package dependecies are located.\n */\nexport type FindPackageDependencyOptions = {\n  /**\n   * Optional starting directory for the search. Defaults to `process.cwd()`.\n   */\n  startDir?: string;\n\n  /**\n   * Optional flag controlling whether symlinks can be found. Defaults to `true`.\n   * When `false`, and the package dependency directory is a symlink, it will not\n   * be found.\n   */\n  allowSymlinks?: boolean;\n\n  /**\n   * Optional flag controlling whether to resolve symlinks. Defaults to `false`.\n   * Note that this flag has no effect if `allowSymlinks` is `false`.\n   */\n  resolveSymlinks?: boolean;\n};\n\n/**\n * Find the package dependency's directory, starting from the given directory\n * and moving outward, through all parent directories.\n *\n * Package dependencies exist under 'node_modules/[`scope`]/[`name`]'.\n *\n * @param ref Package dependency reference\n * @param options Options which control the search\n * @returns Path to the package dependency's directory, or `undefined` if not found.\n */\nexport function findPackageDependencyDir(\n  ref: string | PackageRef,\n  options?: FindPackageDependencyOptions,\n): string | undefined {\n  const pkgName =\n    typeof ref === 'string' ? ref : path.join(ref.scope ?? '', ref.name);\n  const packageDir = findUp.sync(path.join('node_modules', pkgName), {\n    ...pickValues(\n      options ?? {},\n      ['startDir', 'allowSymlinks'],\n      ['cwd', 'allowSymlinks'],\n    ),\n    type: 'directory',\n  });\n  if (!packageDir || !options?.resolveSymlinks) {\n    return packageDir;\n  }\n\n  return fs.lstatSync(packageDir).isSymbolicLink()\n    ? path.resolve(path.dirname(packageDir), fs.readlinkSync(packageDir))\n    : packageDir;\n}\n"], "mappings": ";;;;;;;AAIA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA6B;AAAA;AAAA;AAN7B;AACA;AACA;AACA;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASA,UAAU,CACxBC,GAAM,EACNC,IAAiB,EACjBC,KAAgB,EACqB;EACrC,MAAMC,UAAU,GAAGD,KAAK,IAAID,IAAI;EAChC,MAAMG,OAAgC,GAAG,CAAC,CAAC;EAE3C,IAAIC,WAAW,GAAG,KAAK;EACvB,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGL,IAAI,CAACM,MAAM,EAAE,EAAED,KAAK,EAAE;IAChD,MAAME,KAAK,GAAGR,GAAG,CAACC,IAAI,CAACK,KAAK,CAAC,CAAC;IAC9B,IAAI,OAAOE,KAAK,KAAK,WAAW,EAAE;MAChCJ,OAAO,CAACD,UAAU,CAACG,KAAK,CAAC,CAACG,QAAQ,EAAE,CAAC,GAAGD,KAAK;MAC7CH,WAAW,GAAG,IAAI;IACpB;EACF;EAEA,OAAOA,WAAW,GAAGD,OAAO,GAAGM,SAAS;AAC1C;;AAEA;AACA;AACA;;AAMA;AACA;AACA;;AAqBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,wBAAwB,CACtCC,GAAwB,EACxBC,OAAsC,EAClB;EACpB,MAAMC,OAAO,GACX,OAAOF,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAGG,IAAI,GAACC,IAAI,CAACJ,GAAG,CAACK,KAAK,IAAI,EAAE,EAAEL,GAAG,CAACM,IAAI,CAAC;EACtE,MAAMC,UAAU,GAAGC,iBAAM,CAACC,IAAI,CAACN,IAAI,GAACC,IAAI,CAAC,cAAc,EAAEF,OAAO,CAAC,EAAE;IACjE,GAAGf,UAAU,CACXc,OAAO,IAAI,CAAC,CAAC,EACb,CAAC,UAAU,EAAE,eAAe,CAAC,EAC7B,CAAC,KAAK,EAAE,eAAe,CAAC,CACzB;IACDS,IAAI,EAAE;EACR,CAAC,CAAC;EACF,IAAI,CAACH,UAAU,IAAI,EAACN,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEU,eAAe,GAAE;IAC5C,OAAOJ,UAAU;EACnB;EAEA,OAAOK,EAAE,GAACC,SAAS,CAACN,UAAU,CAAC,CAACO,cAAc,EAAE,GAC5CX,IAAI,GAACY,OAAO,CAACZ,IAAI,GAACa,OAAO,CAACT,UAAU,CAAC,EAAEK,EAAE,GAACK,YAAY,CAACV,UAAU,CAAC,CAAC,GACnEA,UAAU;AAChB"}