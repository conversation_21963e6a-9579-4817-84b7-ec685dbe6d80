{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-7806a930e27e157686c6.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-Debug-7b3feb529019be0099e5.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [1]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2], "name": "appmodules", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-8ebb399378b46721127e.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-Debug-dfe3000f549e8e40bfbd.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-a3a9528e57bc2692f884.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86", "source": "D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}