ninja: Entering directory `D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-gesture-handler\android\.cxx\Debug\2o1j3g12\armeabi-v7a'
[1/2] Building CXX object CMakeFiles/gesturehandler.dir/cpp-adapter.cpp.o
[2/2] Linking CXX shared library "D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-gesture-handler\android\build\intermediates\cxx\Debug\2o1j3g12\obj\armeabi-v7a\libgesturehandler.so"
