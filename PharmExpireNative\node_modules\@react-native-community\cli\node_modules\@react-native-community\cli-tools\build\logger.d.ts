declare const _default: {
    success: (...messages: string[]) => void;
    info: (...messages: string[]) => void;
    warn: (...messages: string[]) => void;
    error: (...messages: string[]) => void;
    debug: (...messages: string[]) => void;
    log: (...messages: string[]) => void;
    setVerbose: (level: boolean) => void;
    isVerbose: () => boolean;
    hasDebugMessages: () => boolean;
    disable: () => void;
    enable: () => void;
};
export default _default;
//# sourceMappingURL=logger.d.ts.map