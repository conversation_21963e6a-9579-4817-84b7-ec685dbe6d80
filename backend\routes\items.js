import express from 'express';
import { Item } from '../models/Item.js';

const router = express.Router();

// Get all items
router.get('/', async (req, res) => {
  try {
    const items = await Item.getAll();
    res.json(items);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get item by ID
router.get('/:id', async (req, res) => {
  try {
    const item = await Item.getById(req.params.id);
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }
    res.json(item);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get items expiring soon
router.get('/filter/expiring-soon', async (req, res) => {
  try {
    const days = req.query.days || 7;
    const items = await Item.getExpiringSoon(days);
    res.json(items);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get expired items
router.get('/filter/expired', async (req, res) => {
  try {
    const items = await Item.getExpired();
    res.json(items);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get items by category
router.get('/category/:category', async (req, res) => {
  try {
    const items = await Item.getByCategory(req.params.category);
    res.json(items);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get statistics
router.get('/stats/dashboard', async (req, res) => {
  try {
    const stats = await Item.getStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Create new item
router.post('/', async (req, res) => {
  try {
    const { item_name, category, expiration_date, notes, quantity, location } = req.body;
    
    // Validation
    if (!item_name || !category || !expiration_date) {
      return res.status(400).json({ 
        error: 'Missing required fields: item_name, category, expiration_date' 
      });
    }

    const validCategories = ['Food', 'Medicine', 'Mom & Baby', 'Cosmetic', 'Wellness'];
    if (!validCategories.includes(category)) {
      return res.status(400).json({ 
        error: 'Invalid category. Must be one of: ' + validCategories.join(', ') 
      });
    }

    const newItem = await Item.create({
      item_name,
      category,
      expiration_date,
      notes: notes || '',
      quantity: quantity || 1,
      location: location || ''
    });
    
    res.status(201).json(newItem);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Update item
router.put('/:id', async (req, res) => {
  try {
    const { item_name, category, expiration_date, notes, quantity, location } = req.body;
    
    // Check if item exists
    const existingItem = await Item.getById(req.params.id);
    if (!existingItem) {
      return res.status(404).json({ error: 'Item not found' });
    }

    // Validation
    if (!item_name || !category || !expiration_date) {
      return res.status(400).json({ 
        error: 'Missing required fields: item_name, category, expiration_date' 
      });
    }

    const validCategories = ['Food', 'Medicine', 'Mom & Baby', 'Cosmetic', 'Wellness'];
    if (!validCategories.includes(category)) {
      return res.status(400).json({ 
        error: 'Invalid category. Must be one of: ' + validCategories.join(', ') 
      });
    }

    const updatedItem = await Item.update(req.params.id, {
      item_name,
      category,
      expiration_date,
      notes: notes || '',
      quantity: quantity || 1,
      location: location || ''
    });
    
    res.json(updatedItem);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Delete item
router.delete('/:id', async (req, res) => {
  try {
    const existingItem = await Item.getById(req.params.id);
    if (!existingItem) {
      return res.status(404).json({ error: 'Item not found' });
    }

    await Item.delete(req.params.id);
    res.json({ message: 'Item deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

export default router;
