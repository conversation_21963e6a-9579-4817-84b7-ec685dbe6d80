#!/usr/bin/env python3
"""
سكريبت لإنشاء أيقونات افتراضية للتطبيق
"""

import os
from PIL import Image, ImageDraw, ImageFont

def create_icon(size, output_path):
    """إنشاء أيقونة بحجم محدد"""
    # إنشاء صورة جديدة بخلفية زرقاء
    img = Image.new('RGBA', (size, size), (37, 130, 235, 255))  # لون أزرق
    draw = ImageDraw.Draw(img)
    
    # رسم صليب أبيض في المنتصف
    cross_width = size // 8
    cross_length = size // 2
    
    center_x = size // 2
    center_y = size // 2
    
    # الخط العمودي للصليب
    draw.rectangle([
        center_x - cross_width//2, 
        center_y - cross_length//2,
        center_x + cross_width//2, 
        center_y + cross_length//2
    ], fill=(255, 255, 255, 255))
    
    # الخط الأفقي للصليب
    draw.rectangle([
        center_x - cross_length//2, 
        center_y - cross_width//2,
        center_x + cross_length//2, 
        center_y + cross_width//2
    ], fill=(255, 255, 255, 255))
    
    # حفظ الصورة
    img.save(output_path, 'PNG')
    print(f"تم إنشاء الأيقونة: {output_path}")

def main():
    """الدالة الرئيسية"""
    base_path = "android/app/src/main/res"
    
    # أحجام الأيقونات المطلوبة
    icon_sizes = {
        'mipmap-mdpi': 48,
        'mipmap-hdpi': 72,
        'mipmap-xhdpi': 96,
        'mipmap-xxhdpi': 144,
        'mipmap-xxxhdpi': 192
    }
    
    for folder, size in icon_sizes.items():
        folder_path = os.path.join(base_path, folder)
        os.makedirs(folder_path, exist_ok=True)
        
        # إنشاء ic_launcher.png
        icon_path = os.path.join(folder_path, 'ic_launcher.png')
        create_icon(size, icon_path)
        
        # إنشاء ic_launcher_round.png (نفس الأيقونة)
        round_icon_path = os.path.join(folder_path, 'ic_launcher_round.png')
        create_icon(size, round_icon_path)
    
    print("✅ تم إنشاء جميع الأيقونات بنجاح!")

if __name__ == "__main__":
    main()
