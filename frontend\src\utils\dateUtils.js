import { format, isAfter, isBefore, addDays, differenceInDays } from 'date-fns';

export const formatDate = (date) => {
  return format(new Date(date), 'MMM dd, yyyy');
};

export const formatDateForInput = (date) => {
  return format(new Date(date), 'yyyy-MM-dd');
};

export const isExpired = (expirationDate) => {
  return isBefore(new Date(expirationDate), new Date());
};

export const isExpiringSoon = (expirationDate, days = 7) => {
  const expDate = new Date(expirationDate);
  const today = new Date();
  const futureDate = addDays(today, days);
  
  return isAfter(expDate, today) && isBefore(expDate, futureDate);
};

export const getDaysUntilExpiration = (expirationDate) => {
  return differenceInDays(new Date(expirationDate), new Date());
};

export const getExpirationStatus = (expirationDate) => {
  const daysUntil = getDaysUntilExpiration(expirationDate);
  
  if (daysUntil < 0) {
    return {
      status: 'expired',
      message: `Expired ${Math.abs(daysUntil)} day${Math.abs(daysUntil) !== 1 ? 's' : ''} ago`,
      color: 'red',
      bgColor: 'bg-red-100',
      textColor: 'text-red-800',
      borderColor: 'border-red-200'
    };
  } else if (daysUntil <= 7) {
    return {
      status: 'expiring-soon',
      message: daysUntil === 0 ? 'Expires today' : `Expires in ${daysUntil} day${daysUntil !== 1 ? 's' : ''}`,
      color: 'yellow',
      bgColor: 'bg-yellow-100',
      textColor: 'text-yellow-800',
      borderColor: 'border-yellow-200'
    };
  } else {
    return {
      status: 'safe',
      message: `Expires in ${daysUntil} day${daysUntil !== 1 ? 's' : ''}`,
      color: 'green',
      bgColor: 'bg-green-100',
      textColor: 'text-green-800',
      borderColor: 'border-green-200'
    };
  }
};

export const getCategoryIcon = (category) => {
  const icons = {
    'Food': '🍎',
    'Medicine': '💊',
    'Mom & Baby': '👶',
    'Cosmetic': '💄',
    'Wellness': '🌿'
  };
  
  return icons[category] || '📦';
};

export const getCategoryColor = (category) => {
  const colors = {
    'Food': 'bg-orange-100 text-orange-800 border-orange-200',
    'Medicine': 'bg-blue-100 text-blue-800 border-blue-200',
    'Mom & Baby': 'bg-pink-100 text-pink-800 border-pink-200',
    'Cosmetic': 'bg-purple-100 text-purple-800 border-purple-200',
    'Wellness': 'bg-green-100 text-green-800 border-green-200'
  };
  
  return colors[category] || 'bg-gray-100 text-gray-800 border-gray-200';
};
