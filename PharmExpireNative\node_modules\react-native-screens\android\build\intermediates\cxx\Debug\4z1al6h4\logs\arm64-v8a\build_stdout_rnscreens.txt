ninja: Entering directory `D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-screens\android\.cxx\Debug\4z1al6h4\arm64-v8a'
[1/6] Building CXX object CMakeFiles/rnscreens.dir/9858fa4c5fe43baa2165615811ad4891/react-native-screens/cpp/RNScreensTurboModule.cpp.o
[2/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o
[3/6] Building CXX object CMakeFiles/rnscreens.dir/1f6f260dc82ef13415ebbfc4b26e5020/cpp/RNSScreenRemovalListener.cpp.o
[4/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/OnLoad.cpp.o
[5/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/NativeProxy.cpp.o
[6/6] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\4z1al6h4\obj\arm64-v8a\librnscreens.so
