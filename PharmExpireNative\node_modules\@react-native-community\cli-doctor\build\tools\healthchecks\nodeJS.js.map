{"version": 3, "names": ["label", "description", "getDiagnostics", "Binaries", "needsToBeFixed", "doesSoftwareNeedToBeFixed", "version", "Node", "versionRange", "versionRanges", "NODE_JS", "runAutomaticFix", "loader", "logManualInstallation", "fail", "healthcheck", "url"], "sources": ["../../../src/tools/healthchecks/nodeJS.ts"], "sourcesContent": ["import versionRanges from '../versionRanges';\nimport {doesSoftwareNeedToBeFixed} from '../checkInstallation';\nimport {HealthCheckInterface} from '../../types';\n\nexport default {\n  label: 'Node.js',\n  description: 'Required to execute JavaScript code',\n  getDiagnostics: async ({Binaries}) => ({\n    needsToBeFixed: doesSoftwareNeedToBeFixed({\n      version: Binaries.Node.version,\n      versionRange: versionRanges.NODE_JS,\n    }),\n    version: Binaries.Node.version,\n    versionRange: versionRanges.NODE_JS,\n  }),\n  runAutomaticFix: async ({loader, logManualInstallation}) => {\n    loader.fail();\n\n    logManualInstallation({\n      healthcheck: 'Node.js',\n      url: 'https://nodejs.org/en/download/',\n    });\n  },\n} as HealthCheckInterface;\n"], "mappings": ";;;;;;AAAA;AACA;AAA+D;AAAA,eAGhD;EACbA,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE,qCAAqC;EAClDC,cAAc,EAAE,OAAO;IAACC;EAAQ,CAAC,MAAM;IACrCC,cAAc,EAAE,IAAAC,4CAAyB,EAAC;MACxCC,OAAO,EAAEH,QAAQ,CAACI,IAAI,CAACD,OAAO;MAC9BE,YAAY,EAAEC,sBAAa,CAACC;IAC9B,CAAC,CAAC;IACFJ,OAAO,EAAEH,QAAQ,CAACI,IAAI,CAACD,OAAO;IAC9BE,YAAY,EAAEC,sBAAa,CAACC;EAC9B,CAAC,CAAC;EACFC,eAAe,EAAE,OAAO;IAACC,MAAM;IAAEC;EAAqB,CAAC,KAAK;IAC1DD,MAAM,CAACE,IAAI,EAAE;IAEbD,qBAAqB,CAAC;MACpBE,WAAW,EAAE,SAAS;MACtBC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ;AACF,CAAC;AAAA"}