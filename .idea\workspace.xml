<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="f88ff612-04d4-4d2c-ac54-281f8e6cba12" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/.bundle/config" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/.eslintrc.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/.prettierrc.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/.watchmanconfig" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/App.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/Gemfile" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/__tests__/App.test.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/app/build.gradle" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/app/debug.keystore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/app/proguard-rules.pro" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/app/src/debug/AndroidManifest.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/app/src/main/AndroidManifest.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/app/src/main/java/com/pharmexpireapp/MainActivity.kt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/app/src/main/java/com/pharmexpireapp/MainApplication.kt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/app/src/main/res/drawable/rn_edit_text_material.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/app/src/main/res/mipmap-hdpi/ic_launcher.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/app/src/main/res/mipmap-hdpi/ic_launcher_round.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/app/src/main/res/mipmap-mdpi/ic_launcher.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/app/src/main/res/mipmap-mdpi/ic_launcher_round.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/app/src/main/res/mipmap-xhdpi/ic_launcher.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/app/src/main/res/mipmap-xhdpi/ic_launcher_round.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/app/src/main/res/mipmap-xxhdpi/ic_launcher.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/app/src/main/res/mipmap-xxhdpi/ic_launcher_round.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/app/src/main/res/mipmap-xxxhdpi/ic_launcher_round.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/app/src/main/res/values/strings.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/app/src/main/res/values/styles.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/build.gradle" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/gradle.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/gradle/wrapper/gradle-wrapper.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/gradle/wrapper/gradle-wrapper.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/gradlew" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/gradlew.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/android/settings.gradle" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/app.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/babel.config.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/ios/.xcode.env" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/ios/PharmExpireApp.xcodeproj/project.pbxproj" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/ios/PharmExpireApp.xcodeproj/xcshareddata/xcschemes/PharmExpireApp.xcscheme" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/ios/PharmExpireApp/AppDelegate.swift" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/ios/PharmExpireApp/Images.xcassets/AppIcon.appiconset/Contents.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/ios/PharmExpireApp/Images.xcassets/Contents.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/ios/PharmExpireApp/Info.plist" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/ios/PharmExpireApp/LaunchScreen.storyboard" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/ios/PharmExpireApp/PrivacyInfo.xcprivacy" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/ios/Podfile" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/jest.config.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/metro.config.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/package-lock.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/package.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/backend/PharmExpireApp/tsconfig.json" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/backend/PharmExpireApp" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 0
}]]></component>
  <component name="ProjectId" id="2zrSWk5uW9UwFu77NaY18KUDekD" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "git-widget-placeholder": "main",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/copy from kpi tracker app/expire management"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="f88ff612-04d4-4d2c-ac54-281f8e6cba12" name="Changes" comment="" />
      <created>1752487732657</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752487732657</updated>
    </task>
    <servers />
  </component>
</project>