{"logs": [{"outputFile": "com.pharmexpirenative.app-mergeDebugResources-41:/values-mn/values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,135,204,285,355,421,497", "endColumns": "79,68,80,69,65,75,74", "endOffsets": "130,199,280,350,416,492,567"}, "to": {"startLines": "48,53,54,56,70,121,122", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4451,4836,4905,5045,6075,10221,10297", "endColumns": "79,68,80,69,65,75,74", "endOffsets": "4526,4900,4981,5110,6136,10292,10367"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\625ed137c6a3f5343b71917adedb437c\\transformed\\appcompat-1.7.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,529,638,724,830,944,1027,1108,1199,1292,1387,1483,1580,1673,1767,1859,1950,2040,2120,2227,2330,2427,2534,2636,2749,2908,9895", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "424,524,633,719,825,939,1022,1103,1194,1287,1382,1478,1575,1668,1762,1854,1945,2035,2115,2222,2325,2422,2529,2631,2744,2903,3002,9971"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\ec4e31fa8d0cf74d3d6955f9c39b3c31\\transformed\\core-1.16.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,123", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3414,3512,3614,3715,3813,3918,4030,10372", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "3507,3609,3710,3808,3913,4025,4144,10468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3e7df312ad5b5ee0d6592d754d6c3af1\\transformed\\material-1.12.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,500,587,672,770,889,974,1035,1100,1198,1279,1338,1431,1493,1556,1614,1685,1747,1801,1922,1979,2040,2094,2165,2298,2382,2462,2558,2641,2724,2857,2939,3017,3149,3239,3319,3373,3424,3490,3561,3639,3710,3789,3864,3942,4022,4105,4210,4298,4377,4467,4560,4634,4704,4795,4849,4929,4996,5080,5165,5227,5291,5354,5425,5529,5644,5741,5855,5913,5968,6052,6139,6215", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,76,78,86,84,97,118,84,60,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,79,95,82,82,132,81,77,131,89,79,53,50,65,70,77,70,78,74,77,79,82,104,87,78,89,92,73,69,90,53,79,66,83,84,61,63,62,70,103,114,96,113,57,54,83,86,75,81", "endOffsets": "260,339,416,495,582,667,765,884,969,1030,1095,1193,1274,1333,1426,1488,1551,1609,1680,1742,1796,1917,1974,2035,2089,2160,2293,2377,2457,2553,2636,2719,2852,2934,3012,3144,3234,3314,3368,3419,3485,3556,3634,3705,3784,3859,3937,4017,4100,4205,4293,4372,4462,4555,4629,4699,4790,4844,4924,4991,5075,5160,5222,5286,5349,5420,5524,5639,5736,5850,5908,5963,6047,6134,6210,6292"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,50,51,52,55,57,58,59,60,61,62,63,64,65,66,67,68,69,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3086,3163,3242,3329,4149,4247,4366,4531,4592,4657,4755,4986,5115,5208,5270,5333,5391,5462,5524,5578,5699,5756,5817,5871,5942,6141,6225,6305,6401,6484,6567,6700,6782,6860,6992,7082,7162,7216,7267,7333,7404,7482,7553,7632,7707,7785,7865,7948,8053,8141,8220,8310,8403,8477,8547,8638,8692,8772,8839,8923,9008,9070,9134,9197,9268,9372,9487,9584,9698,9756,9811,9976,10063,10139", "endLines": "5,33,34,35,36,37,45,46,47,49,50,51,52,55,57,58,59,60,61,62,63,64,65,66,67,68,69,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,78,76,78,86,84,97,118,84,60,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,79,95,82,82,132,81,77,131,89,79,53,50,65,70,77,70,78,74,77,79,82,104,87,78,89,92,73,69,90,53,79,66,83,84,61,63,62,70,103,114,96,113,57,54,83,86,75,81", "endOffsets": "310,3081,3158,3237,3324,3409,4242,4361,4446,4587,4652,4750,4831,5040,5203,5265,5328,5386,5457,5519,5573,5694,5751,5812,5866,5937,6070,6220,6300,6396,6479,6562,6695,6777,6855,6987,7077,7157,7211,7262,7328,7399,7477,7548,7627,7702,7780,7860,7943,8048,8136,8215,8305,8398,8472,8542,8633,8687,8767,8834,8918,9003,9065,9129,9192,9263,9367,9482,9579,9693,9751,9806,9890,10058,10134,10216"}}]}]}