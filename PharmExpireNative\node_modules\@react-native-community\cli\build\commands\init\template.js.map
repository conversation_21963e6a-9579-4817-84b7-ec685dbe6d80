{"version": 3, "names": ["installTemplatePackage", "templateName", "root", "packageManager", "yarnConfigOptions", "logger", "debug", "PackageManager", "init", "silent", "getYarnVersionIfAvailable", "options", "executeCommand", "key", "hasOwnProperty", "value", "install", "getTemplateConfig", "templateSourceDir", "config<PERSON><PERSON><PERSON><PERSON>", "path", "resolve", "fs", "existsSync", "CLIError", "chalk", "underline", "dim", "require", "copyTemplate", "templateDir", "templatePath", "regexStr", "copyFiles", "process", "cwd", "exclude", "RegExp", "replacePathSepForRegex", "executePostInitScript", "postInitScript", "script<PERSON>ath", "execa", "stdio"], "sources": ["../../../src/commands/init/template.ts"], "sourcesContent": ["import execa from 'execa';\nimport path from 'path';\nimport {logger, CLIError} from '@react-native-community/cli-tools';\nimport * as PackageManager from '../../tools/packageManager';\nimport copyFiles from '../../tools/copyFiles';\nimport replacePathSepForRegex from '../../tools/replacePathSepForRegex';\nimport fs from 'fs';\nimport chalk from 'chalk';\nimport {getYarnVersionIfAvailable} from '../../tools/yarn';\nimport {executeCommand} from '../../tools/executeCommand';\n\nexport type TemplateConfig = {\n  placeholderName: string;\n  templateDir: string;\n  postInitScript?: string;\n  titlePlaceholder?: string;\n};\n\nexport async function installTemplatePackage(\n  templateName: string,\n  root: string,\n  packageManager: PackageManager.PackageManager,\n  yarnConfigOptions?: Record<string, string>,\n) {\n  logger.debug(`Installing template from ${templateName}`);\n\n  await PackageManager.init({\n    packageManager,\n    silent: true,\n    root,\n  });\n\n  if (packageManager === 'yarn' && getYarnVersionIfAvailable() !== null) {\n    const options = {\n      root,\n      silent: true,\n    };\n\n    // React Native doesn't support PnP, so we need to set nodeLinker to node-modules. Read more here: https://github.com/react-native-community/cli/issues/27#issuecomment-1772626767\n    executeCommand(\n      'yarn',\n      ['config', 'set', 'nodeLinker', 'node-modules'],\n      options,\n    );\n\n    executeCommand(\n      'yarn',\n      ['config', 'set', 'nmHoistingLimits', 'workspaces'],\n      options,\n    );\n\n    for (let key in yarnConfigOptions) {\n      if (yarnConfigOptions.hasOwnProperty(key)) {\n        let value = yarnConfigOptions[key];\n        executeCommand('yarn', ['config', 'set', key, value], options);\n      }\n    }\n  }\n\n  return PackageManager.install([templateName], {\n    packageManager,\n    silent: true,\n    root,\n  });\n}\n\nexport function getTemplateConfig(\n  templateName: string,\n  templateSourceDir: string,\n): TemplateConfig {\n  const configFilePath = path.resolve(\n    templateSourceDir,\n    'node_modules',\n    templateName,\n    'template.config.js',\n  );\n\n  logger.debug(`Getting config from ${configFilePath}`);\n  if (!fs.existsSync(configFilePath)) {\n    throw new CLIError(\n      `Couldn't find the \"${configFilePath} file inside \"${templateName}\" template. Please make sure the template is valid.\n      Read more: ${chalk.underline.dim(\n        'https://github.com/react-native-community/cli/blob/main/docs/init.md#creating-custom-template',\n      )}`,\n    );\n  }\n  return require(configFilePath);\n}\n\nexport async function copyTemplate(\n  templateName: string,\n  templateDir: string,\n  templateSourceDir: string,\n) {\n  const templatePath = path.resolve(\n    templateSourceDir,\n    'node_modules',\n    templateName,\n    templateDir,\n  );\n\n  logger.debug(`Copying template from ${templatePath}`);\n  let regexStr = path.resolve(templatePath, 'node_modules');\n  await copyFiles(templatePath, process.cwd(), {\n    exclude: [new RegExp(replacePathSepForRegex(regexStr))],\n  });\n}\n\nexport function executePostInitScript(\n  templateName: string,\n  postInitScript: string,\n  templateSourceDir: string,\n) {\n  const scriptPath = path.resolve(\n    templateSourceDir,\n    'node_modules',\n    templateName,\n    postInitScript,\n  );\n\n  logger.debug(`Executing post init script located ${scriptPath}`);\n\n  return execa(scriptPath, {stdio: 'inherit'});\n}\n"], "mappings": ";;;;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AAA0D;AAAA;AAAA;AASnD,eAAeA,sBAAsB,CAC1CC,YAAoB,EACpBC,IAAY,EACZC,cAA6C,EAC7CC,iBAA0C,EAC1C;EACAC,kBAAM,CAACC,KAAK,CAAE,4BAA2BL,YAAa,EAAC,CAAC;EAExD,MAAMM,cAAc,CAACC,IAAI,CAAC;IACxBL,cAAc;IACdM,MAAM,EAAE,IAAI;IACZP;EACF,CAAC,CAAC;EAEF,IAAIC,cAAc,KAAK,MAAM,IAAI,IAAAO,+BAAyB,GAAE,KAAK,IAAI,EAAE;IACrE,MAAMC,OAAO,GAAG;MACdT,IAAI;MACJO,MAAM,EAAE;IACV,CAAC;;IAED;IACA,IAAAG,8BAAc,EACZ,MAAM,EACN,CAAC,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,cAAc,CAAC,EAC/CD,OAAO,CACR;IAED,IAAAC,8BAAc,EACZ,MAAM,EACN,CAAC,QAAQ,EAAE,KAAK,EAAE,kBAAkB,EAAE,YAAY,CAAC,EACnDD,OAAO,CACR;IAED,KAAK,IAAIE,GAAG,IAAIT,iBAAiB,EAAE;MACjC,IAAIA,iBAAiB,CAACU,cAAc,CAACD,GAAG,CAAC,EAAE;QACzC,IAAIE,KAAK,GAAGX,iBAAiB,CAACS,GAAG,CAAC;QAClC,IAAAD,8BAAc,EAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAEC,GAAG,EAAEE,KAAK,CAAC,EAAEJ,OAAO,CAAC;MAChE;IACF;EACF;EAEA,OAAOJ,cAAc,CAACS,OAAO,CAAC,CAACf,YAAY,CAAC,EAAE;IAC5CE,cAAc;IACdM,MAAM,EAAE,IAAI;IACZP;EACF,CAAC,CAAC;AACJ;AAEO,SAASe,iBAAiB,CAC/BhB,YAAoB,EACpBiB,iBAAyB,EACT;EAChB,MAAMC,cAAc,GAAGC,eAAI,CAACC,OAAO,CACjCH,iBAAiB,EACjB,cAAc,EACdjB,YAAY,EACZ,oBAAoB,CACrB;EAEDI,kBAAM,CAACC,KAAK,CAAE,uBAAsBa,cAAe,EAAC,CAAC;EACrD,IAAI,CAACG,aAAE,CAACC,UAAU,CAACJ,cAAc,CAAC,EAAE;IAClC,MAAM,KAAIK,oBAAQ,EACf,sBAAqBL,cAAe,iBAAgBlB,YAAa;AACxE,mBAAmBwB,gBAAK,CAACC,SAAS,CAACC,GAAG,CAC9B,+FAA+F,CAC/F,EAAC,CACJ;EACH;EACA,OAAOC,OAAO,CAACT,cAAc,CAAC;AAChC;AAEO,eAAeU,YAAY,CAChC5B,YAAoB,EACpB6B,WAAmB,EACnBZ,iBAAyB,EACzB;EACA,MAAMa,YAAY,GAAGX,eAAI,CAACC,OAAO,CAC/BH,iBAAiB,EACjB,cAAc,EACdjB,YAAY,EACZ6B,WAAW,CACZ;EAEDzB,kBAAM,CAACC,KAAK,CAAE,yBAAwByB,YAAa,EAAC,CAAC;EACrD,IAAIC,QAAQ,GAAGZ,eAAI,CAACC,OAAO,CAACU,YAAY,EAAE,cAAc,CAAC;EACzD,MAAM,IAAAE,kBAAS,EAACF,YAAY,EAAEG,OAAO,CAACC,GAAG,EAAE,EAAE;IAC3CC,OAAO,EAAE,CAAC,IAAIC,MAAM,CAAC,IAAAC,+BAAsB,EAACN,QAAQ,CAAC,CAAC;EACxD,CAAC,CAAC;AACJ;AAEO,SAASO,qBAAqB,CACnCtC,YAAoB,EACpBuC,cAAsB,EACtBtB,iBAAyB,EACzB;EACA,MAAMuB,UAAU,GAAGrB,eAAI,CAACC,OAAO,CAC7BH,iBAAiB,EACjB,cAAc,EACdjB,YAAY,EACZuC,cAAc,CACf;EAEDnC,kBAAM,CAACC,KAAK,CAAE,sCAAqCmC,UAAW,EAAC,CAAC;EAEhE,OAAO,IAAAC,gBAAK,EAACD,UAAU,EAAE;IAACE,KAAK,EAAE;EAAS,CAAC,CAAC;AAC9C"}