import {
  mockItems,
  calculateStats,
  getExpiredItems,
  getExpiringSoonItems,
  getItemsByCategory,
  addNewItem,
  deleteItem,
  updateItem
} from '../data/mockData';

// محاكاة تأخير الشبكة
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// محاكاة استجابة API
const mockResponse = (data) => ({
  data,
  status: 200,
  statusText: 'OK'
});

// خدمات API للمنتجات (باستخدام البيانات الوهمية)
export const itemsAPI = {
  // الحصول على جميع المنتجات
  getAll: async () => {
    await delay(500); // محاكاة تأخير الشبكة
    return mockResponse(mockItems);
  },

  // الحصول على منتج بواسطة ID
  getById: async (id) => {
    await delay(300);
    const item = mockItems.find(item => item.id === parseInt(id));
    if (item) {
      return mockResponse(item);
    } else {
      throw new Error('المنتج غير موجود');
    }
  },

  // الحصول على المنتجات التي تنتهي قريباً
  getExpiringSoon: async (days = 7) => {
    await delay(400);
    return mockResponse(getExpiringSoonItems(days));
  },

  // الحصول على المنتجات المنتهية
  getExpired: async () => {
    await delay(400);
    return mockResponse(getExpiredItems());
  },

  // الحصول على المنتجات بواسطة الفئة
  getByCategory: async (category) => {
    await delay(400);
    return mockResponse(getItemsByCategory(category));
  },

  // الحصول على إحصائيات لوحة التحكم
  getStats: async () => {
    await delay(600);
    return mockResponse(calculateStats());
  },

  // إنشاء منتج جديد
  create: async (itemData) => {
    await delay(800);
    const newItem = addNewItem(itemData);
    return mockResponse(newItem);
  },

  // تحديث منتج
  update: async (id, itemData) => {
    await delay(700);
    const updatedItem = updateItem(parseInt(id), itemData);
    if (updatedItem) {
      return mockResponse(updatedItem);
    } else {
      throw new Error('فشل في تحديث المنتج');
    }
  },

  // حذف منتج
  delete: async (id) => {
    await delay(500);
    const success = deleteItem(parseInt(id));
    if (success) {
      return mockResponse({ message: 'تم حذف المنتج بنجاح' });
    } else {
      throw new Error('فشل في حذف المنتج');
    }
  },
};

// فحص صحة الخدمة
export const healthCheck = async () => {
  await delay(200);
  return mockResponse({
    status: 'OK',
    message: 'Pharmacy Expire Management API is running (Mock Mode)',
    timestamp: new Date().toISOString()
  });
};

export default { itemsAPI, healthCheck };
