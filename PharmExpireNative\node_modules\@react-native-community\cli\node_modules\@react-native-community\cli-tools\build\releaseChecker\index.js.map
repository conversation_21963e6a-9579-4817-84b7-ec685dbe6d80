{"version": 3, "names": ["getReactNativeVersion", "projectRoot", "require", "path", "join", "resolveNodeModuleDir", "version", "logIfUpdateAvailable", "versions", "latest", "upgrade", "semver", "gt", "stable", "current", "printNewRelease", "name", "currentVersion", "getLatestRelease", "e", "logger", "debug", "found", "parse", "UnknownProjectError", "undefined"], "sources": ["../../src/releaseChecker/index.ts"], "sourcesContent": ["import path from 'path';\nimport semver, {SemVer} from 'semver';\n\nimport {UnknownProjectError} from '../errors';\nimport logger from '../logger';\nimport resolveNodeModuleDir from '../resolveNodeModuleDir';\nimport getLatestRelease, {type Release} from './getLatestRelease';\nimport printNewRelease from './printNewRelease';\n\nconst getReactNativeVersion = (projectRoot: string): string | undefined =>\n  require(path.join(\n    resolveNodeModuleDir(projectRoot, 'react-native'),\n    'package.json',\n  ))?.version;\n\n/**\n * Logs out a message if the user's version is behind a stable version of React Native\n */\nexport async function logIfUpdateAvailable(projectRoot: string): Promise<void> {\n  const versions = await latest(projectRoot);\n  if (!versions?.upgrade) {\n    return;\n  }\n  if (semver.gt(versions.upgrade.stable, versions.current)) {\n    printNewRelease(versions.name, versions.upgrade, versions.current);\n  }\n}\n\ntype Update = {\n  // Only populated if an upgrade is available\n  upgrade?: Release;\n  // The project's package's current version\n  current: string;\n  // The project's package's name\n  name: string;\n};\n\n/**\n * Finds the latest stables version of React Native > current version\n */\nexport async function latest(projectRoot: string): Promise<Update | undefined> {\n  try {\n    const currentVersion = getReactNativeVersion(projectRoot);\n    if (!currentVersion) {\n      return;\n    }\n    const {name} = require(path.join(projectRoot, 'package.json'));\n    const upgrade = await getLatestRelease(name, currentVersion);\n\n    if (upgrade) {\n      return {\n        name,\n        current: currentVersion,\n        upgrade,\n      };\n    }\n  } catch (e) {\n    // We let the flow continue as this component is not vital for the rest of\n    // the CLI.\n    logger.debug(\n      'Cannot detect current version of React Native, ' +\n        'skipping check for a newer release',\n    );\n    logger.debug(e as any);\n  }\n  return;\n}\n\n/**\n * Gets the current project's version parsed as Semver\n */\nexport function current(projectRoot: string): SemVer | undefined {\n  try {\n    const found = semver.parse(getReactNativeVersion(projectRoot));\n    if (found) {\n      return found;\n    }\n  } catch {\n    throw new UnknownProjectError(projectRoot);\n  }\n  return undefined;\n}\n"], "mappings": ";;;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AAAgD;AAEhD,MAAMA,qBAAqB,GAAIC,WAAmB;EAAA;EAAA,mBAChDC,OAAO,CAACC,eAAI,CAACC,IAAI,CACf,IAAAC,6BAAoB,EAACJ,WAAW,EAAE,cAAc,CAAC,EACjD,cAAc,CACf,CAAC,6CAHF,SAGIK,OAAO;AAAA;;AAEb;AACA;AACA;AACO,eAAeC,oBAAoB,CAACN,WAAmB,EAAiB;EAC7E,MAAMO,QAAQ,GAAG,MAAMC,MAAM,CAACR,WAAW,CAAC;EAC1C,IAAI,EAACO,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,OAAO,GAAE;IACtB;EACF;EACA,IAAIC,iBAAM,CAACC,EAAE,CAACJ,QAAQ,CAACE,OAAO,CAACG,MAAM,EAAEL,QAAQ,CAACM,OAAO,CAAC,EAAE;IACxD,IAAAC,wBAAe,EAACP,QAAQ,CAACQ,IAAI,EAAER,QAAQ,CAACE,OAAO,EAAEF,QAAQ,CAACM,OAAO,CAAC;EACpE;AACF;AAWA;AACA;AACA;AACO,eAAeL,MAAM,CAACR,WAAmB,EAA+B;EAC7E,IAAI;IACF,MAAMgB,cAAc,GAAGjB,qBAAqB,CAACC,WAAW,CAAC;IACzD,IAAI,CAACgB,cAAc,EAAE;MACnB;IACF;IACA,MAAM;MAACD;IAAI,CAAC,GAAGd,OAAO,CAACC,eAAI,CAACC,IAAI,CAACH,WAAW,EAAE,cAAc,CAAC,CAAC;IAC9D,MAAMS,OAAO,GAAG,MAAM,IAAAQ,yBAAgB,EAACF,IAAI,EAAEC,cAAc,CAAC;IAE5D,IAAIP,OAAO,EAAE;MACX,OAAO;QACLM,IAAI;QACJF,OAAO,EAAEG,cAAc;QACvBP;MACF,CAAC;IACH;EACF,CAAC,CAAC,OAAOS,CAAC,EAAE;IACV;IACA;IACAC,eAAM,CAACC,KAAK,CACV,iDAAiD,GAC/C,oCAAoC,CACvC;IACDD,eAAM,CAACC,KAAK,CAACF,CAAC,CAAQ;EACxB;EACA;AACF;;AAEA;AACA;AACA;AACO,SAASL,OAAO,CAACb,WAAmB,EAAsB;EAC/D,IAAI;IACF,MAAMqB,KAAK,GAAGX,iBAAM,CAACY,KAAK,CAACvB,qBAAqB,CAACC,WAAW,CAAC,CAAC;IAC9D,IAAIqB,KAAK,EAAE;MACT,OAAOA,KAAK;IACd;EACF,CAAC,CAAC,MAAM;IACN,MAAM,IAAIE,2BAAmB,CAACvB,WAAW,CAAC;EAC5C;EACA,OAAOwB,SAAS;AAClB"}