import axios from 'axios';

// Update this IP address to your computer's local IP address
// You can find it by running 'ipconfig' on Windows or 'ifconfig' on Mac/Linux
const API_BASE_URL = 'http://*************:3001/api'; // Change this IP to your local IP

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000,
});

// Items API
export const itemsAPI = {
  // Get all items
  getAll: () => api.get('/items'),
  
  // Get item by ID
  getById: (id) => api.get(`/items/${id}`),
  
  // Get items expiring soon
  getExpiringSoon: (days = 7) => api.get(`/items/filter/expiring-soon?days=${days}`),
  
  // Get expired items
  getExpired: () => api.get('/items/filter/expired'),
  
  // Get items by category
  getByCategory: (category) => api.get(`/items/category/${category}`),
  
  // Get dashboard statistics
  getStats: () => api.get('/items/stats/dashboard'),
  
  // Create new item
  create: (itemData) => api.post('/items', itemData),
  
  // Update item
  update: (id, itemData) => api.put(`/items/${id}`, itemData),
  
  // Delete item
  delete: (id) => api.delete(`/items/${id}`),
};

// Health check
export const healthCheck = () => api.get('/health');

export default api;
