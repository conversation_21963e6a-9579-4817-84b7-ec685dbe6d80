[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: x86_64", "file_": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\android\\app\\.cxx\\Debug\\2e4v5m5x\\x86_64\\android_gradle_build.json due to:", "file_": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Program Files\\\\Java\\\\jdk-17\\\\bin\\\\java\" ^\n  --class-path ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\modules-2\\\\files-2.1\\\\com.google.prefab\\\\cli\\\\2.1.0\\\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\\\cli-2.1.0-all.jar\" ^\n  com.google.prefab.cli.AppKt ^\n  --build-system ^\n  cmake ^\n  --platform ^\n  android ^\n  --abi ^\n  x86_64 ^\n  --os-version ^\n  24 ^\n  --stl ^\n  c++_shared ^\n  --ndk-version ^\n  27 ^\n  --output ^\n  \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\agp-prefab-staging9866354999296485578\\\\staged-cli-output\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.14.1\\\\transforms\\\\1d221509c521a46e74f6572064742339\\\\transformed\\\\react-android-0.80.1-debug\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.14.1\\\\transforms\\\\816951bc71efb4c99db481b15113489d\\\\transformed\\\\hermes-android-0.80.1-debug\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.14.1\\\\transforms\\\\b6a6ccfe82730aba80016e7d9940f54a\\\\transformed\\\\fbjni-0.7.0\\\\prefab\"\n", "file_": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\android\\app\\.cxx\\Debug\\2e4v5m5x\\x86_64'", "file_": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\android\\app\\.cxx\\Debug\\2e4v5m5x\\x86_64'", "file_": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\copy from kpi tracker app\\\\expire management\\\\PharmExpireNative\\\\node_modules\\\\react-native\\\\ReactAndroid\\\\cmake-utils\\\\default-app-setup\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=x86_64\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86_64\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\copy from kpi tracker app\\\\expire management\\\\PharmExpireNative\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\2e4v5m5x\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\copy from kpi tracker app\\\\expire management\\\\PharmExpireNative\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\2e4v5m5x\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=D:\\\\copy from kpi tracker app\\\\expire management\\\\PharmExpireNative\\\\android\\\\app\\\\.cxx\\\\Debug\\\\2e4v5m5x\\\\prefab\\\\x86_64\\\\prefab\" ^\n  \"-BD:\\\\copy from kpi tracker app\\\\expire management\\\\PharmExpireNative\\\\android\\\\app\\\\.cxx\\\\Debug\\\\2e4v5m5x\\\\x86_64\" ^\n  -GNinja ^\n  \"-DPROJECT_BUILD_DIR=D:\\\\copy from kpi tracker app\\\\expire management\\\\PharmExpireNative\\\\android\\\\app\\\\build\" ^\n  \"-DPROJECT_ROOT_DIR=D:\\\\copy from kpi tracker app\\\\expire management\\\\PharmExpireNative\\\\android\" ^\n  \"-DREACT_ANDROID_DIR=D:\\\\copy from kpi tracker app\\\\expire management\\\\PharmExpireNative\\\\node_modules\\\\react-native\\\\ReactAndroid\" ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\"\n", "file_": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\copy from kpi tracker app\\\\expire management\\\\PharmExpireNative\\\\node_modules\\\\react-native\\\\ReactAndroid\\\\cmake-utils\\\\default-app-setup\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=x86_64\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86_64\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\copy from kpi tracker app\\\\expire management\\\\PharmExpireNative\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\2e4v5m5x\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\copy from kpi tracker app\\\\expire management\\\\PharmExpireNative\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\2e4v5m5x\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=D:\\\\copy from kpi tracker app\\\\expire management\\\\PharmExpireNative\\\\android\\\\app\\\\.cxx\\\\Debug\\\\2e4v5m5x\\\\prefab\\\\x86_64\\\\prefab\" ^\n  \"-BD:\\\\copy from kpi tracker app\\\\expire management\\\\PharmExpireNative\\\\android\\\\app\\\\.cxx\\\\Debug\\\\2e4v5m5x\\\\x86_64\" ^\n  -GNinja ^\n  \"-DPROJECT_BUILD_DIR=D:\\\\copy from kpi tracker app\\\\expire management\\\\PharmExpireNative\\\\android\\\\app\\\\build\" ^\n  \"-DPROJECT_ROOT_DIR=D:\\\\copy from kpi tracker app\\\\expire management\\\\PharmExpireNative\\\\android\" ^\n  \"-DREACT_ANDROID_DIR=D:\\\\copy from kpi tracker app\\\\expire management\\\\PharmExpireNative\\\\node_modules\\\\react-native\\\\ReactAndroid\" ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\"\n", "file_": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\android\\app\\.cxx\\Debug\\2e4v5m5x\\x86_64\\compile_commands.json.bin normally", "file_": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\android\\app\\.cxx\\Debug\\2e4v5m5x\\x86_64\\compile_commands.json to D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\android\\app\\.cxx\\tools\\debug\\x86_64\\compile_commands.json", "file_": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]