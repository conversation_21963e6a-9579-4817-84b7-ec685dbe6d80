<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="70a5b919-5e49-4479-a407-e8d5368e97b5" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/../.bundle/config" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../.eslintrc.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../.prettierrc.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../.watchmanconfig" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../App.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../Gemfile" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../__tests__/App.test.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/build.gradle" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/debug.keystore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/proguard-rules.pro" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/src/debug/AndroidManifest.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/src/main/AndroidManifest.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/src/main/java/com/pharmexpirenative/MainActivity.kt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/src/main/java/com/pharmexpirenative/MainApplication.kt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/src/main/res/drawable/rn_edit_text_material.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/src/main/res/mipmap-hdpi/ic_launcher.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/src/main/res/mipmap-hdpi/ic_launcher_round.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/src/main/res/mipmap-mdpi/ic_launcher.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/src/main/res/mipmap-mdpi/ic_launcher_round.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/src/main/res/mipmap-xhdpi/ic_launcher.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/src/main/res/mipmap-xhdpi/ic_launcher_round.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/src/main/res/mipmap-xxhdpi/ic_launcher.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/src/main/res/mipmap-xxhdpi/ic_launcher_round.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/src/main/res/mipmap-xxxhdpi/ic_launcher_round.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/src/main/res/values/strings.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/src/main/res/values/styles.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/build.gradle" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/gradle.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/gradle/wrapper/gradle-wrapper.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/gradle/wrapper/gradle-wrapper.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/gradlew" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/gradlew.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/settings.gradle" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../app.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../babel.config.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../ios/.xcode.env" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../ios/PharmExpireNative.xcodeproj/project.pbxproj" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../ios/PharmExpireNative.xcodeproj/xcshareddata/xcschemes/PharmExpireNative.xcscheme" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../ios/PharmExpireNative/AppDelegate.swift" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../ios/PharmExpireNative/Images.xcassets/AppIcon.appiconset/Contents.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../ios/PharmExpireNative/Images.xcassets/Contents.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../ios/PharmExpireNative/Info.plist" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../ios/PharmExpireNative/LaunchScreen.storyboard" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../ios/PharmExpireNative/PrivacyInfo.xcprivacy" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../ios/Podfile" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../jest.config.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../metro.config.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../package-lock.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../package.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../tsconfig.json" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[DeviceId(pluginId=PhysicalDevice, isTemplate=false, identifier=serial=RFCR70ZL5XY)]" />
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <projects_view>
          <tree_state>
            <expand />
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 1
}]]></component>
  <component name="ProjectId" id="2ztDIk7CVKt4bE1MqBiMFoa6f8b" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Android App.app.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "git-widget-placeholder": "main",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/copy from kpi tracker app/expire management/PharmExpireNative/android"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="app" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="PharmExpireNative.app" />
      <option name="ANDROID_RUN_CONFIGURATION_SCHEMA_VERSION" value="1" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="ALLOW_ASSUME_VERIFIED" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="RESTORE_ENABLED" value="false" />
      <option name="RESTORE_FILE" value="" />
      <option name="RESTORE_FRESH_INSTALL_ONLY" value="false" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="70a5b919-5e49-4479-a407-e8d5368e97b5" name="Changes" comment="" />
      <created>1752541397527</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752541397527</updated>
    </task>
    <servers />
  </component>
</project>