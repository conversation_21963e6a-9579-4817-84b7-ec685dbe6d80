# 📱 دليل استبدال أيقونة التطبيق

## 🎯 الخطوات المطلوبة:

### 1. تحضير الأيقونات:
بعد إنشاء الأيقونات بالأحجام المختلفة، ضعها في المجلدات التالية:

```
PharmExpireNative/android/app/src/main/res/
├── mipmap-mdpi/ic_launcher.png      (48x48 px)
├── mipmap-hdpi/ic_launcher.png      (72x72 px)
├── mipmap-xhdpi/ic_launcher.png     (96x96 px)
├── mipmap-xxhdpi/ic_launcher.png    (144x144 px)
└── mipmap-xxxhdpi/ic_launcher.png   (192x192 px)
```

### 2. أوامر النسخ (إذا كانت الأيقونات في مجلد icons):

```bash
# إذا كانت أيقوناتك في مجلد icons
cp icons/ic_launcher_48.png android/app/src/main/res/mipmap-mdpi/ic_launcher.png
cp icons/ic_launcher_72.png android/app/src/main/res/mipmap-hdpi/ic_launcher.png
cp icons/ic_launcher_96.png android/app/src/main/res/mipmap-xhdpi/ic_launcher.png
cp icons/ic_launcher_144.png android/app/src/main/res/mipmap-xxhdpi/ic_launcher.png
cp icons/ic_launcher_192.png android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png
```

### 3. إعادة بناء التطبيق:

```bash
# تنظيف المشروع
cd android
./gradlew clean

# العودة للمجلد الرئيسي
cd ..

# إعادة تشغيل التطبيق
npx react-native run-android
```

## 🌐 مواقع مفيدة لإنشاء الأيقونات:

1. **App Icon Generator**: https://appicon.co/
2. **Icon Kitchen**: https://icon.kitchen/
3. **Android Asset Studio**: https://romannurik.github.io/AndroidAssetStudio/

## 📐 مواصفات الأيقونة المثالية:

- **الحجم الأساسي**: 512x512 بكسل
- **التنسيق**: PNG
- **الخلفية**: شفافة (مفضل)
- **التصميم**: بسيط وواضح
- **الألوان**: متباينة وجذابة

## ⚠️ ملاحظات مهمة:

1. **احتفظ بنسخة احتياطية** من الأيقونات الأصلية
2. **تأكد من الأحجام الصحيحة** لكل مجلد
3. **أعد تشغيل التطبيق** بعد تغيير الأيقونات
4. **اختبر على أجهزة مختلفة** للتأكد من الوضوح

## 🎨 نصائح التصميم:

- استخدم ألوان تتناسب مع موضوع الصيدلية
- اجعل الأيقونة بسيطة ومميزة
- تأكد من وضوحها في الأحجام الصغيرة
- استخدم رموز طبية أو صيدلانية مناسبة
