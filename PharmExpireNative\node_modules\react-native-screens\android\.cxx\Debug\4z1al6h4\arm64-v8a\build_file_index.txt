D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-screens\android\.cxx\Debug\4z1al6h4\prefab\arm64-v8a\prefab\lib\aarch64-linux-android\cmake\fbjni\fbjniConfig.cmake
D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-screens\android\.cxx\Debug\4z1al6h4\prefab\arm64-v8a\prefab\lib\aarch64-linux-android\cmake\fbjni\fbjniConfigVersion.cmake
D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-screens\android\.cxx\Debug\4z1al6h4\prefab\arm64-v8a\prefab\lib\aarch64-linux-android\cmake\ReactAndroid\ReactAndroidConfig.cmake
D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-screens\android\.cxx\Debug\4z1al6h4\prefab\arm64-v8a\prefab\lib\aarch64-linux-android\cmake\ReactAndroid\ReactAndroidConfigVersion.cmake
D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-screens\android\CMakeLists.txt