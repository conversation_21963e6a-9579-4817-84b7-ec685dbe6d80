{"version": 3, "names": [], "sources": ["../../../src/commands/init/types.ts"], "sourcesContent": ["import type {PackageManager} from '../../tools/packageManager';\n\nexport type Options = {\n  template?: string;\n  npm?: boolean;\n  pm?: PackageManager;\n  directory?: string;\n  displayName?: string;\n  title?: string;\n  skipInstall?: boolean;\n  version: string;\n  packageName?: string;\n  installPods?: string | boolean;\n  platformName?: string;\n  skipGitInit?: boolean;\n  replaceDirectory?: string | boolean;\n  yarnConfigOptions?: Record<string, string>;\n};\n"], "mappings": ""}