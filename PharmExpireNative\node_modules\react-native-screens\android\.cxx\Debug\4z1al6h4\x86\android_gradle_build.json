{"buildFiles": ["D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\4z1al6h4\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\fbjni\\fbjniConfig.cmake", "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\4z1al6h4\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\fbjni\\fbjniConfigVersion.cmake", "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\4z1al6h4\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\4z1al6h4\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-screens\\android\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\4z1al6h4\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\4z1al6h4\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"rnscreens::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86", "artifactName": "rnscreens", "output": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx\\Debug\\4z1al6h4\\obj\\x86\\librnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b6a6ccfe82730aba80016e7d9940f54a\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}