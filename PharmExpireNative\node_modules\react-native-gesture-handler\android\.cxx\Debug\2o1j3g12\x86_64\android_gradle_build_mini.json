{"buildFiles": ["D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\2o1j3g12\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\2o1j3g12\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"gesturehandler::@6890427a1f51a3e7e1df": {"artifactName": "gesturehandler", "abi": "x86_64", "output": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx\\Debug\\2o1j3g12\\obj\\x86_64\\libgesturehandler.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b6a6ccfe82730aba80016e7d9940f54a\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so"]}}}