import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { itemsAPI } from '../services/api';
import { calculateStats } from '../data/mockData';

const { width } = Dimensions.get('window');

const DashboardScreen = ({ navigation }) => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      setLoading(true);
      // استخدام البيانات الوهمية المحلية
      const statsData = calculateStats();
      setStats(statsData);
    } catch (error) {
      console.error('خطأ في جلب الإحصائيات:', error);
      Alert.alert('خطأ', 'فشل في تحميل إحصائيات لوحة التحكم');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchStats();
    setRefreshing(false);
  };

  const StatCard = ({ title, value, icon, color, onPress }) => (
    <TouchableOpacity style={[styles.statCard, { borderLeftColor: color }]} onPress={onPress}>
      <View style={styles.statCardContent}>
        <View style={styles.statCardLeft}>
          <Text style={styles.statCardTitle}>{title}</Text>
          <Text style={[styles.statCardValue, { color }]}>{value}</Text>
        </View>
        <View style={[styles.statCardIcon, { backgroundColor: color }]}>
          <Icon name={icon} size={24} color="#fff" />
        </View>
      </View>
    </TouchableOpacity>
  );

  const CategoryCard = ({ name, count, icon, color, onPress }) => (
    <TouchableOpacity style={[styles.categoryCard, { borderColor: color }]} onPress={onPress}>
      <Text style={styles.categoryIcon}>{icon}</Text>
      <Text style={styles.categoryName}>{name}</Text>
      <Text style={[styles.categoryCount, { color }]}>{count}</Text>
    </TouchableOpacity>
  );

  const QuickActionCard = ({ title, description, icon, color, onPress }) => (
    <TouchableOpacity style={styles.quickActionCard} onPress={onPress}>
      <View style={[styles.quickActionIcon, { backgroundColor: color }]}>
        <Icon name={icon} size={24} color="#fff" />
      </View>
      <View style={styles.quickActionContent}>
        <Text style={styles.quickActionTitle}>{title}</Text>
        <Text style={styles.quickActionDescription}>{description}</Text>
      </View>
    </TouchableOpacity>
  );

  if (loading && !stats) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>جاري تحميل لوحة التحكم...</Text>
      </View>
    );
  }

  // التحقق من وجود منتجات
  const hasItems = stats && stats.totalProducts > 0;

  const categoryStats = [
    {
      name: 'أدوية',
      count: stats?.categoryStats?.medicine || 0,
      icon: '💊',
      color: '#3b82f6',
      category: 'medicine'
    },
    {
      name: 'أم وطفل',
      count: stats?.categoryStats?.['mom&baby'] || 0,
      icon: '👶',
      color: '#ec4899',
      category: 'mom&baby'
    },
    {
      name: 'عناية شخصية',
      count: stats?.categoryStats?.personal_care || 0,
      icon: '🧴',
      color: '#f97316',
      category: 'personal_care'
    },
    {
      name: 'مكملات غذائية',
      count: stats?.categoryStats?.nutraceutical || 0,
      icon: '🌿',
      color: '#10b981',
      category: 'nutraceutical'
    },
    {
      name: 'تجميل',
      count: stats?.categoryStats?.beauty || 0,
      icon: '💄',
      color: '#8b5cf6',
      category: 'beauty'
    },
  ].filter(category => category.count > 0); // إخفاء الفئات الفارغة

  return (
    <ScrollView
      style={styles.container}
      refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
    >
      {/* العنوان الرئيسي */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>مرحباً بك في PharmExpire</Text>
        <Text style={styles.headerSubtitle}>إدارة صيدليتك بذكاء</Text>
        {hasItems ? (
          <Text style={styles.headerStats}>
            إجمالي {stats.totalProducts} منتج • {stats.totalItems} قطعة
          </Text>
        ) : (
          <Text style={styles.headerStats}>
            ابدأ بإضافة منتجاتك الأولى
          </Text>
        )}
      </View>

      {/* رسالة ترحيب للمستخدمين الجدد */}
      {!hasItems && (
        <View style={styles.welcomeContainer}>
          <View style={styles.welcomeCard}>
            <Text style={styles.welcomeIcon}>🏥</Text>
            <Text style={styles.welcomeTitle}>مرحباً بك في صيدليتك الذكية!</Text>
            <Text style={styles.welcomeDescription}>
              لا توجد منتجات في مخزونك حتى الآن.{'\n'}
              ابدأ بإضافة منتجاتك لتتمكن من:{'\n'}
              • تتبع تواريخ انتهاء الصلاحية{'\n'}
              • الحصول على تنبيهات ذكية{'\n'}
              • إدارة مخزونك بكفاءة
            </Text>
            <TouchableOpacity
              style={styles.welcomeButton}
              onPress={() => navigation.navigate('Add')}
            >
              <Icon name="add-circle" size={20} color="#fff" style={styles.welcomeButtonIcon} />
              <Text style={styles.welcomeButtonText}>إضافة منتج جديد</Text>
            </TouchableOpacity>

            {/* نصائح سريعة */}
            <View style={styles.tipsContainer}>
              <Text style={styles.tipsTitle}>💡 نصائح سريعة:</Text>
              <Text style={styles.tipText}>• استخدم الكاميرا لتصوير المنتجات</Text>
              <Text style={styles.tipText}>• اختر التاريخ بدقة لتجنب انتهاء الصلاحية</Text>
              <Text style={styles.tipText}>• صنف منتجاتك حسب الفئة المناسبة</Text>
            </View>
          </View>
        </View>
      )}

      {/* بطاقات الإحصائيات - تظهر فقط عند وجود منتجات */}
      {hasItems && (
        <View style={styles.statsContainer}>
          <StatCard
            title="إجمالي المنتجات"
            value={stats?.totalItems || 0}
            icon="inventory"
            color="#3b82f6"
            onPress={() => navigation.navigate('Items')}
          />
          <StatCard
            title="منتجات منتهية"
            value={stats?.expiredItems || 0}
            icon="warning"
            color="#dc2626"
            onPress={() => navigation.navigate('Items', { filter: 'expired' })}
          />
          <StatCard
            title="تنتهي قريباً"
            value={stats?.expiringSoonItems || 0}
            icon="schedule"
            color="#d97706"
            onPress={() => navigation.navigate('Items', { filter: 'expiring' })}
          />
          <StatCard
            title="منتجات آمنة"
            value={stats?.safeItems || 0}
            icon="check-circle"
            color="#10b981"
            onPress={() => navigation.navigate('Items', { filter: 'safe' })}
          />
        </View>
      )}

      {/* قسم الفئات - يظهر فقط عند وجود منتجات */}
      {hasItems && categoryStats.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>المنتجات حسب الفئة</Text>
          <View style={styles.categoriesContainer}>
            {categoryStats.map((category, index) => (
              <CategoryCard
                key={index}
                name={category.name}
                count={category.count}
                icon={category.icon}
                color={category.color}
                onPress={() => navigation.navigate('Items', { filter: 'category', category: category.category })}
              />
            ))}
          </View>
        </View>
      )}

      {/* الإجراءات السريعة */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>الإجراءات السريعة</Text>
        <QuickActionCard
          title="إضافة منتج جديد"
          description="أضف منتجات جديدة إلى مخزونك"
          icon="add-circle"
          color="#10b981"
          onPress={() => navigation.navigate('Add')}
        />
        <QuickActionCard
          title="عرض جميع المنتجات"
          description="تصفح مخزونك الكامل"
          icon="list"
          color="#3b82f6"
          onPress={() => navigation.navigate('Items')}
        />
        <QuickActionCard
          title="المنتجات المنتهية"
          description="تحقق من المنتجات التي تحتاج انتباه"
          icon="warning"
          color="#dc2626"
          onPress={() => navigation.navigate('Items', { filter: 'expired' })}
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
  },
  loadingText: {
    fontSize: 16,
    color: '#6b7280',
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  headerStats: {
    fontSize: 14,
    color: '#9ca3af',
    textAlign: 'center',
    marginTop: 8,
    fontWeight: '500',
  },
  statsContainer: {
    padding: 20,
    gap: 12,
  },
  statCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statCardContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statCardLeft: {
    flex: 1,
  },
  statCardTitle: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 4,
    textAlign: 'right',
  },
  statCardValue: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'right',
  },
  statCardIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
    textAlign: 'right',
  },
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  categoryCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    width: (width - 64) / 2,
    borderWidth: 1,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  categoryIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 4,
    textAlign: 'center',
  },
  categoryCount: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  quickActionCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  quickActionContent: {
    flex: 1,
  },
  quickActionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
    textAlign: 'right',
  },
  quickActionDescription: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'right',
  },
  // ستايلات شاشة الترحيب
  welcomeContainer: {
    padding: 20,
  },
  welcomeCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  welcomeIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  welcomeTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 12,
    textAlign: 'center',
  },
  welcomeDescription: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  welcomeButton: {
    backgroundColor: '#10b981',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  welcomeButtonIcon: {
    marginRight: 8,
  },
  welcomeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  // ستايلات النصائح
  tipsContainer: {
    marginTop: 20,
    padding: 16,
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  tipsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  tipText: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 4,
    lineHeight: 16,
  },
});

export default DashboardScreen;
