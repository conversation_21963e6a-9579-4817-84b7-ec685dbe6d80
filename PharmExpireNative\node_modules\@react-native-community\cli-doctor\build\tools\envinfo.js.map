{"version": 3, "names": ["getEnvironmentInfo", "json", "options", "showNotFound", "packages", "outOfTreePlatforms", "darwin", "win32", "outOfTreePlatformPackage", "platform", "push", "info", "en<PERSON><PERSON>", "run", "System", "Binaries", "IDEs", "Managers", "Languages", "SDKs", "npmPackages", "npmGlobalPackages", "JSON", "parse", "trim"], "sources": ["../../src/tools/envinfo.ts"], "sourcesContent": ["import envinfo from 'envinfo';\nimport {platform} from 'os';\nimport {EnvironmentInfo} from '../types';\n\n/**\n * Returns information about the running system.\n * If `json === true`, or no options are passed,\n * the return type will be an `EnvironmentInfo`.\n * If set to `false`, it will be a `string`.\n */\nasync function getEnvironmentInfo(): Promise<EnvironmentInfo>;\nasync function getEnvironmentInfo(json: true): Promise<EnvironmentInfo>;\nasync function getEnvironmentInfo(json: false): Promise<string>;\nasync function getEnvironmentInfo(\n  json = true,\n): Promise<string | EnvironmentInfo> {\n  const options = {json, showNotFound: true};\n\n  const packages = ['react', 'react-native', '@react-native-community/cli'];\n\n  const outOfTreePlatforms: {[key: string]: string} = {\n    darwin: 'react-native-macos',\n    win32: 'react-native-windows',\n  };\n\n  const outOfTreePlatformPackage = outOfTreePlatforms[platform()];\n  if (outOfTreePlatformPackage) {\n    packages.push(outOfTreePlatformPackage);\n  }\n\n  const info = (await envinfo.run(\n    {\n      System: ['OS', 'CPU', 'Memory', 'Shell'],\n      Binaries: ['Node', 'Yarn', 'npm', 'Watchman'],\n      IDEs: ['Xcode', 'Android Studio', 'Visual Studio'],\n      Managers: ['CocoaPods'],\n      Languages: ['Java', 'Ruby'],\n      SDKs: ['iOS SDK', 'Android SDK', 'Windows SDK'],\n      npmPackages: packages,\n      npmGlobalPackages: ['*react-native*'],\n    },\n    options,\n  )) as string;\n\n  if (options.json) {\n    return JSON.parse(info);\n  }\n\n  return info.trim();\n}\n\nexport default getEnvironmentInfo;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA4B;AAG5B;AACA;AACA;AACA;AACA;AACA;;AAIA,eAAeA,kBAAkB,CAC/BC,IAAI,GAAG,IAAI,EACwB;EACnC,MAAMC,OAAO,GAAG;IAACD,IAAI;IAAEE,YAAY,EAAE;EAAI,CAAC;EAE1C,MAAMC,QAAQ,GAAG,CAAC,OAAO,EAAE,cAAc,EAAE,6BAA6B,CAAC;EAEzE,MAAMC,kBAA2C,GAAG;IAClDC,MAAM,EAAE,oBAAoB;IAC5BC,KAAK,EAAE;EACT,CAAC;EAED,MAAMC,wBAAwB,GAAGH,kBAAkB,CAAC,IAAAI,cAAQ,GAAE,CAAC;EAC/D,IAAID,wBAAwB,EAAE;IAC5BJ,QAAQ,CAACM,IAAI,CAACF,wBAAwB,CAAC;EACzC;EAEA,MAAMG,IAAI,GAAI,MAAMC,kBAAO,CAACC,GAAG,CAC7B;IACEC,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC;IACxCC,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,CAAC;IAC7CC,IAAI,EAAE,CAAC,OAAO,EAAE,gBAAgB,EAAE,eAAe,CAAC;IAClDC,QAAQ,EAAE,CAAC,WAAW,CAAC;IACvBC,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;IAC3BC,IAAI,EAAE,CAAC,SAAS,EAAE,aAAa,EAAE,aAAa,CAAC;IAC/CC,WAAW,EAAEhB,QAAQ;IACrBiB,iBAAiB,EAAE,CAAC,gBAAgB;EACtC,CAAC,EACDnB,OAAO,CACG;EAEZ,IAAIA,OAAO,CAACD,IAAI,EAAE;IAChB,OAAOqB,IAAI,CAACC,KAAK,CAACZ,IAAI,CAAC;EACzB;EAEA,OAAOA,IAAI,CAACa,IAAI,EAAE;AACpB;AAAC,eAEcxB,kBAAkB;AAAA"}