@echo off
"C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-HD:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=24" ^
  "-DANDROID_PLATFORM=android-24" ^
  "-DANDROID_ABI=x86_64" ^
  "-DCMAKE_ANDROID_ARCH_ABI=x86_64" ^
  "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006" ^
  "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006" ^
  "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake" ^
  "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_CXX_FLAGS=-O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx\\Debug\\2o1j3g12\\obj\\x86_64" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx\\Debug\\2o1j3g12\\obj\\x86_64" ^
  "-DCMAKE_BUILD_TYPE=Debug" ^
  "-DCMAKE_FIND_ROOT_PATH=D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\2o1j3g12\\prefab\\x86_64\\prefab" ^
  "-BD:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\2o1j3g12\\x86_64" ^
  -GNinja ^
  "-DREACT_NATIVE_DIR=D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\node_modules\\react-native" ^
  "-DREACT_NATIVE_MINOR_VERSION=80" ^
  "-DANDROID_STL=c++_shared" ^
  "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"
