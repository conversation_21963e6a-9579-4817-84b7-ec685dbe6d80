/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleCpp.js
 */

#include "RNVectorIconsSpecJSI.h"

namespace facebook::react {

static jsi::Value __hostFunction_NativeRNVectorIconsCxxSpecJSI_getImageForFont(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNVectorIconsCxxSpecJSI *>(&turboModule)->getImageForFont(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asNum<PERSON>(),
    count <= 3 ? throw jsi::JSError(rt, "Expected argument in position 3 to be passed") : args[3].asNumber()
  );
}
static jsi::Value __hostFunction_NativeRNVectorIconsCxxSpecJSI_getImageForFontSync(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNVectorIconsCxxSpecJSI *>(&turboModule)->getImageForFontSync(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asNumber(),
    count <= 3 ? throw jsi::JSError(rt, "Expected argument in position 3 to be passed") : args[3].asNumber()
  );
}
static jsi::Value __hostFunction_NativeRNVectorIconsCxxSpecJSI_loadFontWithFileName(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNVectorIconsCxxSpecJSI *>(&turboModule)->loadFontWithFileName(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt)
  );
}

NativeRNVectorIconsCxxSpecJSI::NativeRNVectorIconsCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("RNVectorIcons", jsInvoker) {
  methodMap_["getImageForFont"] = MethodMetadata {4, __hostFunction_NativeRNVectorIconsCxxSpecJSI_getImageForFont};
  methodMap_["getImageForFontSync"] = MethodMetadata {4, __hostFunction_NativeRNVectorIconsCxxSpecJSI_getImageForFontSync};
  methodMap_["loadFontWithFileName"] = MethodMetadata {2, __hostFunction_NativeRNVectorIconsCxxSpecJSI_loadFontWithFileName};
}


} // namespace facebook::react
