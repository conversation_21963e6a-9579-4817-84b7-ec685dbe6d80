/**
 * PharmExpire - تطبيق إدارة انتهاء صلاحية الصيدلية
 * نسخة مبسطة للتشغيل في Android Studio
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  FlatList,
  Modal,
  StatusBar,
} from 'react-native';
import axios from 'axios';

// عنوان API - قم بتغييره حسب عنوان IP الخاص بك
const API_BASE_URL = 'http://************:3001/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000,
});

// خدمات API
const itemsAPI = {
  getAll: () => api.get('/items'),
  getStats: () => api.get('/items/stats/dashboard'),
  create: (itemData) => api.post('/items', itemData),
  update: (id, itemData) => api.put(`/items/${id}`, itemData),
  delete: (id) => api.delete(`/items/${id}`),
};

// أدوات التاريخ
const formatDate = (date) => {
  const d = new Date(date);
  return d.toLocaleDateString('ar-SA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const getExpirationStatus = (expirationDate) => {
  const expDate = new Date(expirationDate);
  const today = new Date();
  const diffTime = expDate - today;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays < 0) {
    return {
      status: 'expired',
      message: `انتهت منذ ${Math.abs(diffDays)} يوم`,
      color: '#dc2626',
      backgroundColor: '#fef2f2',
      textColor: '#991b1b'
    };
  } else if (diffDays <= 7) {
    return {
      status: 'expiring-soon',
      message: diffDays === 0 ? 'تنتهي اليوم' : `تنتهي خلال ${diffDays} يوم`,
      color: '#d97706',
      backgroundColor: '#fffbeb',
      textColor: '#92400e'
    };
  } else {
    return {
      status: 'safe',
      message: `تنتهي خلال ${diffDays} يوم`,
      color: '#059669',
      backgroundColor: '#ecfdf5',
      textColor: '#065f46'
    };
  }
};

const getCategoryIcon = (category) => {
  const icons = {
    'Food': '🍎',
    'Medicine': '💊',
    'Mom & Baby': '👶',
    'Cosmetic': '💄',
    'Wellness': '🌿'
  };
  return icons[category] || '📦';
};

// مكون لوحة التحكم
const DashboardScreen = ({ onNavigate }) => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      setLoading(true);
      const response = await itemsAPI.getStats();
      setStats(response.data);
    } catch (error) {
      console.error('خطأ في جلب الإحصائيات:', error);
      Alert.alert('خطأ', 'فشل في تحميل إحصائيات لوحة التحكم');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>جاري تحميل لوحة التحكم...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>مرحباً بك في PharmExpire</Text>
        <Text style={styles.headerSubtitle}>إدارة صيدليتك بذكاء</Text>
      </View>

      <View style={styles.statsContainer}>
        <TouchableOpacity style={[styles.statCard, { borderLeftColor: '#3b82f6' }]} onPress={() => onNavigate('items')}>
          <Text style={styles.statCardTitle}>إجمالي المنتجات</Text>
          <Text style={[styles.statCardValue, { color: '#3b82f6' }]}>{stats?.total_items || 0}</Text>
        </TouchableOpacity>

        <TouchableOpacity style={[styles.statCard, { borderLeftColor: '#dc2626' }]}>
          <Text style={styles.statCardTitle}>منتجات منتهية</Text>
          <Text style={[styles.statCardValue, { color: '#dc2626' }]}>{stats?.expired_items || 0}</Text>
        </TouchableOpacity>

        <TouchableOpacity style={[styles.statCard, { borderLeftColor: '#d97706' }]}>
          <Text style={styles.statCardTitle}>تنتهي قريباً</Text>
          <Text style={[styles.statCardValue, { color: '#d97706' }]}>{stats?.expiring_soon || 0}</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.actionContainer}>
        <TouchableOpacity style={styles.actionButton} onPress={() => onNavigate('add')}>
          <Text style={styles.actionButtonText}>➕ إضافة منتج جديد</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton} onPress={() => onNavigate('items')}>
          <Text style={styles.actionButtonText}>📦 عرض جميع المنتجات</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

// مكون قائمة المنتجات
const ItemListScreen = ({ onNavigate }) => {
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchItems();
  }, []);

  const fetchItems = async () => {
    try {
      setLoading(true);
      const response = await itemsAPI.getAll();
      setItems(response.data);
    } catch (error) {
      console.error('خطأ في جلب المنتجات:', error);
      Alert.alert('خطأ', 'فشل في تحميل المنتجات');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteItem = async (id) => {
    Alert.alert(
      'حذف المنتج',
      'هل أنت متأكد من حذف هذا المنتج؟',
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'حذف',
          style: 'destructive',
          onPress: async () => {
            try {
              await itemsAPI.delete(id);
              setItems(items.filter(item => item.id !== id));
              Alert.alert('نجح', 'تم حذف المنتج بنجاح');
            } catch (error) {
              console.error('خطأ في حذف المنتج:', error);
              Alert.alert('خطأ', 'فشل في حذف المنتج');
            }
          },
        },
      ]
    );
  };

  const renderItem = ({ item }) => {
    const expirationStatus = getExpirationStatus(item.expiration_date);
    
    return (
      <View style={[styles.itemCard, { borderRightColor: expirationStatus.color }]}>
        <View style={styles.itemHeader}>
          <View style={styles.itemInfo}>
            <Text style={styles.itemName}>{item.item_name}</Text>
            <Text style={styles.itemCategory}>{getCategoryIcon(item.category)} {item.category}</Text>
          </View>
          <TouchableOpacity onPress={() => handleDeleteItem(item.id)}>
            <Text style={styles.deleteButton}>🗑️</Text>
          </TouchableOpacity>
        </View>

        <View style={[styles.expirationBadge, { backgroundColor: expirationStatus.backgroundColor }]}>
          <Text style={[styles.expirationText, { color: expirationStatus.textColor }]}>
            {expirationStatus.message}
          </Text>
          <Text style={[styles.expirationDate, { color: expirationStatus.textColor }]}>
            تاريخ الانتهاء: {formatDate(item.expiration_date)}
          </Text>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>جاري تحميل المنتجات...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>جميع المنتجات</Text>
        <TouchableOpacity onPress={() => onNavigate('add')}>
          <Text style={styles.addButtonHeader}>➕ إضافة</Text>
        </TouchableOpacity>
      </View>

      <FlatList
        data={items}
        renderItem={renderItem}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyTitle}>لا توجد منتجات</Text>
            <Text style={styles.emptySubtitle}>أضف منتجك الأول للبدء</Text>
          </View>
        }
      />
    </View>
  );
};

// مكون إضافة المنتجات
const AddItemScreen = ({ onNavigate }) => {
  const [formData, setFormData] = useState({
    item_name: '',
    category: '',
    expiration_date: '',
    notes: '',
    quantity: '1',
    location: '',
  });
  const [loading, setLoading] = useState(false);
  const [showCategoryPicker, setShowCategoryPicker] = useState(false);

  const categories = [
    { value: 'Food', label: 'طعام', icon: '🍎' },
    { value: 'Medicine', label: 'أدوية', icon: '💊' },
    { value: 'Mom & Baby', label: 'أم وطفل', icon: '👶' },
    { value: 'Cosmetic', label: 'مستحضرات تجميل', icon: '💄' },
    { value: 'Wellness', label: 'صحة ولياقة', icon: '🌿' }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async () => {
    if (!formData.item_name.trim()) {
      Alert.alert('خطأ', 'اسم المنتج مطلوب');
      return;
    }
    if (!formData.category) {
      Alert.alert('خطأ', 'يرجى اختيار فئة');
      return;
    }
    if (!formData.expiration_date) {
      Alert.alert('خطأ', 'تاريخ انتهاء الصلاحية مطلوب');
      return;
    }

    setLoading(true);
    try {
      const submitData = {
        ...formData,
        quantity: parseInt(formData.quantity) || 1,
      };

      await itemsAPI.create(submitData);
      Alert.alert('نجح', 'تم إضافة المنتج بنجاح');
      onNavigate('items');
    } catch (error) {
      console.error('خطأ في حفظ المنتج:', error);
      Alert.alert('خطأ', 'فشل في حفظ المنتج');
    } finally {
      setLoading(false);
    }
  };

  const selectedCategory = categories.find(cat => cat.value === formData.category);

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>إضافة منتج جديد</Text>
      </View>

      <View style={styles.form}>
        <View style={styles.inputGroup}>
          <Text style={styles.label}>اسم المنتج *</Text>
          <TextInput
            style={styles.input}
            value={formData.item_name}
            onChangeText={(text) => handleInputChange('item_name', text)}
            placeholder="أدخل اسم المنتج"
            textAlign="right"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>الفئة *</Text>
          <TouchableOpacity
            style={styles.pickerButton}
            onPress={() => setShowCategoryPicker(true)}
          >
            <Text style={styles.pickerText}>
              {selectedCategory ? `${selectedCategory.icon} ${selectedCategory.label}` : 'اختر الفئة'}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>تاريخ انتهاء الصلاحية *</Text>
          <TextInput
            style={styles.input}
            value={formData.expiration_date}
            onChangeText={(text) => handleInputChange('expiration_date', text)}
            placeholder="YYYY-MM-DD"
            textAlign="right"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>الكمية</Text>
          <TextInput
            style={styles.input}
            value={formData.quantity}
            onChangeText={(text) => handleInputChange('quantity', text)}
            placeholder="1"
            keyboardType="numeric"
            textAlign="right"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>الموقع</Text>
          <TextInput
            style={styles.input}
            value={formData.location}
            onChangeText={(text) => handleInputChange('location', text)}
            placeholder="مثال: الرف أ"
            textAlign="right"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>ملاحظات</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={formData.notes}
            onChangeText={(text) => handleInputChange('notes', text)}
            placeholder="ملاحظات إضافية"
            multiline
            numberOfLines={3}
            textAlign="right"
          />
        </View>

        <TouchableOpacity
          style={styles.submitButton}
          onPress={handleSubmit}
          disabled={loading}
        >
          <Text style={styles.submitButtonText}>
            {loading ? 'جاري الحفظ...' : 'إضافة المنتج'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.cancelButton}
          onPress={() => onNavigate('dashboard')}
        >
          <Text style={styles.cancelButtonText}>إلغاء</Text>
        </TouchableOpacity>
      </View>

      <Modal
        visible={showCategoryPicker}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCategoryPicker(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>اختر الفئة</Text>
            {categories.map((category) => (
              <TouchableOpacity
                key={category.value}
                style={styles.categoryOption}
                onPress={() => {
                  handleInputChange('category', category.value);
                  setShowCategoryPicker(false);
                }}
              >
                <Text style={styles.categoryOptionText}>
                  {category.icon} {category.label}
                </Text>
              </TouchableOpacity>
            ))}
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowCategoryPicker(false)}
            >
              <Text style={styles.modalCloseText}>إغلاق</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
};

// التطبيق الرئيسي
const App = () => {
  const [currentScreen, setCurrentScreen] = useState('dashboard');

  const renderScreen = () => {
    switch (currentScreen) {
      case 'items':
        return <ItemListScreen onNavigate={setCurrentScreen} />;
      case 'add':
        return <AddItemScreen onNavigate={setCurrentScreen} />;
      default:
        return <DashboardScreen onNavigate={setCurrentScreen} />;
    }
  };

  return (
    <View style={styles.appContainer}>
      <StatusBar barStyle="light-content" backgroundColor="#2563eb" />
      {renderScreen()}
      
      {/* شريط التنقل السفلي */}
      <View style={styles.bottomNav}>
        <TouchableOpacity
          style={[styles.navButton, currentScreen === 'dashboard' && styles.navButtonActive]}
          onPress={() => setCurrentScreen('dashboard')}
        >
          <Text style={styles.navButtonText}>🏠 الرئيسية</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.navButton, currentScreen === 'items' && styles.navButtonActive]}
          onPress={() => setCurrentScreen('items')}
        >
          <Text style={styles.navButtonText}>📦 المنتجات</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.navButton, currentScreen === 'add' && styles.navButtonActive]}
          onPress={() => setCurrentScreen('add')}
        >
          <Text style={styles.navButtonText}>➕ إضافة</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  appContainer: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
  },
  loadingText: {
    fontSize: 16,
    color: '#6b7280',
  },
  header: {
    padding: 20,
    backgroundColor: '#2563eb',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#e5e7eb',
    textAlign: 'center',
    marginTop: 4,
  },
  addButtonHeader: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  statsContainer: {
    padding: 20,
    gap: 12,
  },
  statCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
    elevation: 2,
  },
  statCardTitle: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 4,
    textAlign: 'right',
  },
  statCardValue: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'right',
  },
  actionContainer: {
    padding: 20,
    gap: 12,
  },
  actionButton: {
    backgroundColor: '#2563eb',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  listContainer: {
    padding: 16,
  },
  itemCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderRightWidth: 4,
    elevation: 2,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
    textAlign: 'right',
  },
  itemCategory: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'right',
  },
  deleteButton: {
    fontSize: 20,
    padding: 8,
  },
  expirationBadge: {
    padding: 12,
    borderRadius: 8,
  },
  expirationText: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
    textAlign: 'right',
  },
  expirationDate: {
    fontSize: 12,
    opacity: 0.8,
    textAlign: 'right',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 64,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
  },
  form: {
    padding: 20,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
    textAlign: 'right',
  },
  input: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 14,
    backgroundColor: '#fff',
    color: '#1f2937',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  pickerButton: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    backgroundColor: '#fff',
  },
  pickerText: {
    fontSize: 14,
    color: '#1f2937',
    textAlign: 'right',
  },
  submitButton: {
    backgroundColor: '#2563eb',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 12,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  cancelButton: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  cancelButtonText: {
    color: '#374151',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    width: '80%',
    maxHeight: '70%',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
    textAlign: 'center',
  },
  categoryOption: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  categoryOptionText: {
    fontSize: 16,
    color: '#1f2937',
    textAlign: 'right',
  },
  modalCloseButton: {
    backgroundColor: '#6b7280',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  modalCloseText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  bottomNav: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    elevation: 8,
  },
  navButton: {
    flex: 1,
    padding: 12,
    alignItems: 'center',
  },
  navButtonActive: {
    backgroundColor: '#eff6ff',
  },
  navButtonText: {
    fontSize: 12,
    color: '#6b7280',
    fontWeight: '500',
  },
});

export default App;
