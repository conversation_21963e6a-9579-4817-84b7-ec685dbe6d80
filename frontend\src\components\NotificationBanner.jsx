import React, { useState, useEffect } from 'react';
import { AlertTriangle, X, Calendar, Package } from 'lucide-react';
import { itemsAPI } from '../services/api';
import { formatDate } from '../utils/dateUtils';

const NotificationBanner = ({ onNavigate }) => {
  const [notifications, setNotifications] = useState([]);
  const [dismissed, setDismissed] = useState(new Set());
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchNotifications();
  }, []);

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const [expiredResponse, expiringSoonResponse] = await Promise.all([
        itemsAPI.getExpired(),
        itemsAPI.getExpiringSoon(7)
      ]);

      const notifications = [];

      // Add expired items notification
      if (expiredResponse.data.length > 0) {
        notifications.push({
          id: 'expired',
          type: 'error',
          title: 'Expired Items Alert',
          message: `${expiredResponse.data.length} item${expiredResponse.data.length !== 1 ? 's have' : ' has'} expired`,
          action: 'View Expired Items',
          actionCallback: () => onNavigate('expired'),
          items: expiredResponse.data.slice(0, 3) // Show first 3 items
        });
      }

      // Add expiring soon notification
      if (expiringSoonResponse.data.length > 0) {
        notifications.push({
          id: 'expiring',
          type: 'warning',
          title: 'Items Expiring Soon',
          message: `${expiringSoonResponse.data.length} item${expiringSoonResponse.data.length !== 1 ? 's are' : ' is'} expiring within 7 days`,
          action: 'View Expiring Items',
          actionCallback: () => onNavigate('expiring'),
          items: expiringSoonResponse.data.slice(0, 3) // Show first 3 items
        });
      }

      setNotifications(notifications);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const dismissNotification = (id) => {
    setDismissed(prev => new Set([...prev, id]));
  };

  const visibleNotifications = notifications.filter(notification => 
    !dismissed.has(notification.id)
  );

  if (loading || visibleNotifications.length === 0) {
    return null;
  }

  return (
    <div className="space-y-3 mb-6">
      {visibleNotifications.map((notification) => (
        <div
          key={notification.id}
          className={`rounded-lg border-l-4 p-4 ${
            notification.type === 'error'
              ? 'bg-red-50 border-red-400'
              : 'bg-yellow-50 border-yellow-400'
          }`}
        >
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3 flex-1">
              <div className={`p-1 rounded-full ${
                notification.type === 'error' ? 'bg-red-100' : 'bg-yellow-100'
              }`}>
                {notification.type === 'error' ? (
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                ) : (
                  <Calendar className="h-5 w-5 text-yellow-600" />
                )}
              </div>
              
              <div className="flex-1">
                <h3 className={`font-medium ${
                  notification.type === 'error' ? 'text-red-800' : 'text-yellow-800'
                }`}>
                  {notification.title}
                </h3>
                <p className={`text-sm mt-1 ${
                  notification.type === 'error' ? 'text-red-700' : 'text-yellow-700'
                }`}>
                  {notification.message}
                </p>
                
                {/* Show sample items */}
                {notification.items && notification.items.length > 0 && (
                  <div className="mt-2">
                    <div className="flex flex-wrap gap-2">
                      {notification.items.map((item, index) => (
                        <span
                          key={item.id}
                          className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                            notification.type === 'error'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}
                        >
                          <Package className="h-3 w-3" />
                          {item.item_name}
                          <span className="opacity-75">
                            ({formatDate(item.expiration_date)})
                          </span>
                        </span>
                      ))}
                      {notifications.find(n => n.id === notification.id)?.items?.length > 3 && (
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          notification.type === 'error'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          +{notifications.find(n => n.id === notification.id).items.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex items-center gap-2 ml-4">
              <button
                onClick={notification.actionCallback}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                  notification.type === 'error'
                    ? 'bg-red-100 text-red-800 hover:bg-red-200'
                    : 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
                }`}
              >
                {notification.action}
              </button>
              
              <button
                onClick={() => dismissNotification(notification.id)}
                className={`p-1 rounded-md transition-colors ${
                  notification.type === 'error'
                    ? 'text-red-400 hover:text-red-600 hover:bg-red-100'
                    : 'text-yellow-400 hover:text-yellow-600 hover:bg-yellow-100'
                }`}
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default NotificationBanner;
