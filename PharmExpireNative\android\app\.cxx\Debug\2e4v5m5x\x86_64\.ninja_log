# ninja log v5
6	55	0	D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86_64/CMakeFiles/cmake.verify_globs	b2e3a2591e075b8f
716	5576	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	8935a15a44f3da59
1622	6402	7742461903501734	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	5a3f1d365e58afd1
2	6621	7742461905998516	CMakeFiles/appmodules.dir/OnLoad.cpp.o	824de3777abf1da
1164	7366	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	a4e49f2528c36214
2127	7734	7742461917317014	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/EventEmitters.cpp.o	d4c981a7915d56a3
931	7771	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o	36add5d7585d9b30
1406	8004	7742461919974518	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	1a4e0e936967d000
480	8166	7742461921484040	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	2ffb701eb08c13c4
1911	8490	7742461924897455	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	f8db00595e0453f9
3616	8600	7742461926082723	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	ca3c7f865681273f
250	8662	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	8f5a4653cdf5db1c
2643	8813	7742461928220771	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	64f6d6368d1d73eb
2385	9458	7742461934578811	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/ComponentDescriptors.cpp.o	de52cfa1750bde67
3915	9666	0	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	81a40890f112d53
4287	10269	7742461942786132	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	8606119e4c4d8f4a
2913	11270	7742461952568594	CMakeFiles/appmodules.dir/3d65668b9a8facb0ef0b395fec5f0274/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	fc07cfa12d748eba
11271	13210	7742461972102408	D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/build/intermediates/cxx/Debug/2e4v5m5x/obj/x86_64/libappmodules.so	48ed540c0fb42fed
