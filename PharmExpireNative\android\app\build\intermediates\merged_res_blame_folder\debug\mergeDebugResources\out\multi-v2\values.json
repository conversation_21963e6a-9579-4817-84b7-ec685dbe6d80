{"logs": [{"outputFile": "com.pharmexpirenative.app-mergeDebugResources-27:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\625ed137c6a3f5343b71917adedb437c\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,18,19,20,21,22,23,24,25,26,27,28,29,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,167,168,169,170,171,172,173,174,175,191,192,193,194,195,196,197,198,248,249,250,251,254,262,263,268,289,300,301,302,303,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,387,401,402,403,404,405,418,426,427,431,435,439,444,450,457,461,465,470,474,478,482,486,490,494,500,504,510,514,520,524,529,533,536,540,546,550,556,560,566,569,573,577,581,585,589,590,591,592,595,598,601,604,608,609,610,611,612,615,617,619,621,626,627,631,637,641,642,644,656,657,661,667,671,672,673,677,704,708,709,713,741,913,939,1110,1136,1167,1175,1181,1197,1219,1224,1229,1239,1248,1257,1261,1268,1287,1294,1295,1304,1307,1310,1314,1318,1322,1325,1326,1331,1336,1346,1351,1358,1364,1365,1368,1372,1377,1379,1381,1384,1387,1389,1393,1396,1403,1406,1409,1413,1415,1419,1421,1423,1425,1429,1437,1445,1457,1463,1472,1475,1486,1489,1490,1495,1496,1520,1589,1659,1660,1670,1679,1680,1682,1686,1689,1692,1695,1698,1701,1704,1707,1711,1714,1717,1720,1724,1727,1731,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1763,1765,1766,1767,1768,1769,1770,1771,1772,1774,1775,1777,1778,1780,1782,1783,1785,1786,1787,1788,1789,1790,1792,1793,1794,1795,1796,1808,1810,1812,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1829,1830,1831,1832,1833,1834,1835,1837,1841,1903,1904,1905,1906,1907,1908,1912,1913,1914,1915,1917,1919,1921,1923,1925,1926,1927,1928,1930,1932,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1948,1949,1950,1951,1953,1955,1956,1958,1959,1961,1963,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1978,1979,1980,1981,1983,1984,1985,1986,1987,1989,1991,1993,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2056,2131,2134,2137,2140,2154,2160,2202,2205,2234,2261,2270,2334,2711,2721,2759,2787,3043,3067,3073,3079,3100,3224,3244,3250,3254,3260,3378,3414,3480,3500,3555,3567,3593", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,314,355,410,472,536,606,667,742,818,895,1133,1218,1300,1376,1452,1529,1607,1713,1819,1898,1978,2035,2350,2424,2499,2564,2630,2690,2751,2823,2896,2963,3031,3090,3149,3208,3267,3326,3380,3434,3487,3541,3595,3649,3835,3909,3988,4061,4135,4206,4278,4350,4423,4480,4538,4611,4685,4759,4834,4906,4979,5049,5120,5180,5241,5310,5379,5449,5523,5599,5663,5740,5816,5893,5958,6027,6104,6179,6248,6316,6393,6459,6520,6617,6682,6751,6850,6921,6980,7038,7095,7154,7218,7289,7361,7433,7505,7577,7644,7712,7780,7839,7902,7966,8056,8147,8207,8273,8340,8406,8476,8540,8593,8660,8721,8788,8901,8959,9022,9087,9152,9227,9300,9372,9416,9463,9509,9558,9619,9680,9741,9803,9867,9931,9995,10060,10123,10183,10244,10310,10369,10429,10491,10562,10622,11246,11332,11419,11509,11596,11684,11766,11849,11939,13008,13060,13118,13163,13229,13293,13350,13407,16315,16372,16420,16469,16613,16951,16998,17231,18335,18873,18937,18999,19059,19250,19324,19394,19472,19526,19596,19681,19729,19775,19836,19899,19965,20029,20100,20163,20228,20292,20353,20414,20466,20539,20613,20682,20757,20831,20905,21046,27622,29499,29577,29667,29755,29851,30620,31202,31291,31538,31819,32071,32356,32749,33226,33448,33670,33946,34173,34403,34633,34863,35093,35320,35739,35965,36390,36620,37048,37267,37550,37758,37889,38116,38542,38767,39194,39415,39840,39960,40236,40537,40861,41152,41466,41603,41734,41839,42081,42248,42452,42660,42931,43043,43155,43260,43377,43591,43737,43877,43963,44311,44399,44645,45063,45312,45394,45492,46149,46249,46501,46925,47180,47274,47363,47600,49624,49866,49968,50221,52377,63058,64574,75269,76797,78554,79180,79600,80861,82126,82382,82618,83165,83659,84264,84462,85042,86410,86785,86903,87441,87598,87794,88067,88323,88493,88634,88698,89063,89430,90106,90370,90708,91061,91155,91341,91647,91909,92034,92161,92400,92611,92730,92923,93100,93555,93736,93858,94117,94230,94417,94519,94626,94755,95030,95538,96034,96911,97205,97775,97924,98656,98828,98912,99248,99340,100626,105857,111228,111290,111868,112452,112543,112656,112885,113045,113197,113368,113534,113703,113870,114033,114276,114446,114619,114790,115064,115263,115468,116138,116222,116318,116414,116512,116612,116714,116816,116918,117020,117122,117222,117318,117430,117559,117682,117813,117944,118042,118156,118250,118390,118524,118620,118732,118832,118948,119044,119156,119256,119396,119532,119696,119826,119984,120134,120275,120419,120554,120666,120816,120944,121072,121208,121340,121470,121600,121712,122610,122756,122900,123064,123130,123220,123296,123400,123490,123592,123700,123808,123908,123988,124080,124178,124288,124340,124418,124524,124616,124720,124830,124952,125115,128553,128633,128733,128823,128933,129023,129264,129358,129464,129556,129656,129768,129882,129998,130114,130208,130322,130434,130536,130656,130778,130860,130964,131084,131210,131308,131402,131490,131602,131718,131840,131952,132127,132243,132329,132421,132533,132657,132724,132850,132918,133046,133190,133318,133387,133482,133597,133710,133809,133918,134029,134140,134241,134346,134446,134576,134667,134790,134884,134996,135082,135186,135282,135370,135488,135592,135696,135822,135910,136018,136118,136208,136318,136402,136504,136588,136642,136706,136812,136898,137008,137092,139678,142294,142412,142527,142607,142968,143201,144605,144683,146027,147388,147776,150619,161258,161596,163267,164624,172702,173453,173715,173915,174294,178572,179178,179407,179558,179773,182301,183322,186348,187092,189223,189563,190874", "endLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,18,19,20,21,22,23,24,25,26,27,28,29,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,167,168,169,170,171,172,173,174,175,191,192,193,194,195,196,197,198,248,249,250,251,254,262,263,268,289,300,301,302,303,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,387,401,402,403,404,405,425,426,430,434,438,443,449,456,460,464,469,473,477,481,485,489,493,499,503,509,513,519,523,528,532,535,539,545,549,555,559,565,568,572,576,580,584,588,589,590,591,594,597,600,603,607,608,609,610,611,614,616,618,620,625,626,630,636,640,641,643,655,656,660,666,670,671,672,676,703,707,708,712,740,912,938,1109,1135,1166,1174,1180,1196,1218,1223,1228,1238,1247,1256,1260,1267,1286,1293,1294,1303,1306,1309,1313,1317,1321,1324,1325,1330,1335,1345,1350,1357,1363,1364,1367,1371,1376,1378,1380,1383,1386,1388,1392,1395,1402,1405,1408,1412,1414,1418,1420,1422,1424,1428,1436,1444,1456,1462,1471,1474,1485,1488,1489,1494,1495,1500,1588,1658,1659,1669,1678,1679,1681,1685,1688,1691,1694,1697,1700,1703,1706,1710,1713,1716,1719,1723,1726,1730,1734,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1762,1764,1765,1766,1767,1768,1769,1770,1771,1773,1774,1776,1777,1779,1781,1782,1784,1785,1786,1787,1788,1789,1791,1792,1793,1794,1795,1796,1809,1811,1813,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1828,1829,1830,1831,1832,1833,1834,1836,1840,1844,1903,1904,1905,1906,1907,1911,1912,1913,1914,1916,1918,1920,1922,1924,1925,1926,1927,1929,1931,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1947,1948,1949,1950,1952,1954,1955,1957,1958,1960,1962,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1977,1978,1979,1980,1982,1983,1984,1985,1986,1988,1990,1992,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2130,2133,2136,2139,2153,2159,2169,2204,2233,2260,2269,2333,2696,2714,2748,2786,2804,3066,3072,3078,3099,3223,3243,3249,3253,3259,3294,3389,3479,3499,3554,3566,3592,3599", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,350,405,467,531,601,662,737,813,890,968,1213,1295,1371,1447,1524,1602,1708,1814,1893,1973,2030,2088,2419,2494,2559,2625,2685,2746,2818,2891,2958,3026,3085,3144,3203,3262,3321,3375,3429,3482,3536,3590,3644,3698,3904,3983,4056,4130,4201,4273,4345,4418,4475,4533,4606,4680,4754,4829,4901,4974,5044,5115,5175,5236,5305,5374,5444,5518,5594,5658,5735,5811,5888,5953,6022,6099,6174,6243,6311,6388,6454,6515,6612,6677,6746,6845,6916,6975,7033,7090,7149,7213,7284,7356,7428,7500,7572,7639,7707,7775,7834,7897,7961,8051,8142,8202,8268,8335,8401,8471,8535,8588,8655,8716,8783,8896,8954,9017,9082,9147,9222,9295,9367,9411,9458,9504,9553,9614,9675,9736,9798,9862,9926,9990,10055,10118,10178,10239,10305,10364,10424,10486,10557,10617,10685,11327,11414,11504,11591,11679,11761,11844,11934,12025,13055,13113,13158,13224,13288,13345,13402,13456,16367,16415,16464,16515,16642,16993,17042,17272,18362,18932,18994,19054,19111,19319,19389,19467,19521,19591,19676,19724,19770,19831,19894,19960,20024,20095,20158,20223,20287,20348,20409,20461,20534,20608,20677,20752,20826,20900,21041,21111,27670,29572,29662,29750,29846,29936,31197,31286,31533,31814,32066,32351,32744,33221,33443,33665,33941,34168,34398,34628,34858,35088,35315,35734,35960,36385,36615,37043,37262,37545,37753,37884,38111,38537,38762,39189,39410,39835,39955,40231,40532,40856,41147,41461,41598,41729,41834,42076,42243,42447,42655,42926,43038,43150,43255,43372,43586,43732,43872,43958,44306,44394,44640,45058,45307,45389,45487,46144,46244,46496,46920,47175,47269,47358,47595,49619,49861,49963,50216,52372,63053,64569,75264,76792,78549,79175,79595,80856,82121,82377,82613,83160,83654,84259,84457,85037,86405,86780,86898,87436,87593,87789,88062,88318,88488,88629,88693,89058,89425,90101,90365,90703,91056,91150,91336,91642,91904,92029,92156,92395,92606,92725,92918,93095,93550,93731,93853,94112,94225,94412,94514,94621,94750,95025,95533,96029,96906,97200,97770,97919,98651,98823,98907,99243,99335,99613,105852,111223,111285,111863,112447,112538,112651,112880,113040,113192,113363,113529,113698,113865,114028,114271,114441,114614,114785,115059,115258,115463,115793,116217,116313,116409,116507,116607,116709,116811,116913,117015,117117,117217,117313,117425,117554,117677,117808,117939,118037,118151,118245,118385,118519,118615,118727,118827,118943,119039,119151,119251,119391,119527,119691,119821,119979,120129,120270,120414,120549,120661,120811,120939,121067,121203,121335,121465,121595,121707,121847,122751,122895,123033,123125,123215,123291,123395,123485,123587,123695,123803,123903,123983,124075,124173,124283,124335,124413,124519,124611,124715,124825,124947,125110,125267,128628,128728,128818,128928,129018,129259,129353,129459,129551,129651,129763,129877,129993,130109,130203,130317,130429,130531,130651,130773,130855,130959,131079,131205,131303,131397,131485,131597,131713,131835,131947,132122,132238,132324,132416,132528,132652,132719,132845,132913,133041,133185,133313,133382,133477,133592,133705,133804,133913,134024,134135,134236,134341,134441,134571,134662,134785,134879,134991,135077,135181,135277,135365,135483,135587,135691,135817,135905,136013,136113,136203,136313,136397,136499,136583,136637,136701,136807,136893,137003,137087,137207,142289,142407,142522,142602,142963,143196,143713,144678,146022,147383,147771,150614,160667,161388,162961,164619,165191,173448,173710,173910,174289,178567,179173,179402,179553,179768,180851,182608,186343,187087,189218,189558,190869,191072"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,94,98,102,105,109,113,117,120,123,124,125,134,141,148,151,154,157,163,166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,181,232,286,345,393,442,491,540,595,652,708,756,805,863,912,948,998,1039,1083,1138,1182,1225,1259,1298,1344,1392,1434,1488,1600,1717,1840,1960,2074,2200,2355,2740,2836,2956,3076,3178,3318,3440,3550,3657,3760,3871,4040,4208,4325,4444,4557,4743,4851,4964,5055,5166,5335,5433,5558,5653,5760,5930,6028,6211,6384,6496,6597,6756,6890,7030,7218,7323,7454,7623,7740,7888,8033,8183,8282,8378,8574,8757,8856,9040,9207,9455,9703,9946,10106,10308,10514,10711,10887,11051,11077,11112,11650,12068,12446,12623,12802,12985,13350,13547", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84,85,86,87,88,89,93,97,101,104,108,112,116,119,122,123,124,133,140,147,150,153,156,162,165,175", "endColumns": "62,62,50,53,58,47,48,48,48,54,56,55,47,48,57,48,35,49,40,43,54,43,42,33,38,45,47,41,53,47,116,122,119,113,125,154,384,95,119,119,101,139,121,109,106,102,110,168,167,116,118,112,185,107,112,90,110,168,97,124,94,106,169,97,182,172,111,100,158,133,139,101,104,130,168,116,147,144,149,98,95,195,182,98,183,166,10,10,12,12,10,10,10,12,12,25,34,10,10,10,10,10,12,12,12,10", "endOffsets": "113,176,227,281,340,388,437,486,535,590,647,703,751,800,858,907,943,993,1034,1078,1133,1177,1220,1254,1293,1339,1387,1429,1483,1531,1712,1835,1955,2069,2195,2350,2735,2831,2951,3071,3173,3313,3435,3545,3652,3755,3866,4035,4203,4320,4439,4552,4738,4846,4959,5050,5161,5330,5428,5553,5648,5755,5925,6023,6206,6379,6491,6592,6751,6885,7025,7127,7318,7449,7618,7735,7883,8028,8178,8277,8373,8569,8752,8851,9035,9202,9450,9698,9941,10101,10303,10509,10706,10882,11046,11072,11107,11645,12063,12441,12618,12797,12980,13345,13542,13983"}, "to": {"startLines": "32,33,202,203,204,237,238,239,240,241,242,243,244,245,246,247,252,255,256,259,260,261,264,266,287,288,290,291,292,293,333,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,385,386,388,389,390,391,392,393,394,395,397,398,399,400,406,410,1501,1505,1508,1512,1516,1735,1738,1814,1861,1862,1871,1878,1885,1888,1891,1894,1900,2046", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2224,2287,13674,13725,13779,15748,15796,15845,15894,15943,15998,16055,16111,16159,16208,16266,16520,16647,16697,16808,16852,16907,17047,17132,18250,18289,18367,18415,18457,18511,21116,21882,22005,22125,22239,22365,22520,22905,23001,23121,23241,23343,23483,23605,23715,23822,23925,24036,24205,24373,24490,24609,24722,24908,25016,25129,25220,25331,25500,25598,25723,25818,25925,26095,26193,26376,26549,26661,26762,26921,27055,27195,27386,27491,27675,27844,27961,28109,28254,28404,28503,28599,28866,29049,29148,29332,29941,30189,99618,99861,100021,100223,100429,115798,115974,123038,126083,126118,126656,127074,127452,127629,127808,127991,128356,139237", "endLines": "32,33,202,203,204,237,238,239,240,241,242,243,244,245,246,247,252,255,256,259,260,261,264,266,287,288,290,291,292,293,333,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,385,386,388,389,390,391,392,393,394,395,397,398,399,400,409,413,1504,1507,1511,1515,1519,1737,1740,1814,1861,1870,1877,1884,1887,1890,1893,1899,1902,2055", "endColumns": "62,62,50,53,58,47,48,48,48,54,56,55,47,48,57,48,35,49,40,43,54,43,42,33,38,45,47,41,53,47,116,122,119,113,125,154,384,95,119,119,101,139,121,109,106,102,110,168,167,116,118,112,185,107,112,90,110,168,97,124,94,106,169,97,182,172,111,100,158,133,139,101,104,130,168,116,147,144,149,98,95,195,182,98,183,166,10,10,12,12,10,10,10,12,12,25,34,10,10,10,10,10,12,12,12,10", "endOffsets": "2282,2345,13720,13774,13833,15791,15840,15889,15938,15993,16050,16106,16154,16203,16261,16310,16551,16692,16733,16847,16902,16946,17085,17161,18284,18330,18410,18452,18506,18554,21228,22000,22120,22234,22360,22515,22900,22996,23116,23236,23338,23478,23600,23710,23817,23920,24031,24200,24368,24485,24604,24717,24903,25011,25124,25215,25326,25495,25593,25718,25813,25920,26090,26188,26371,26544,26656,26757,26916,27050,27190,27292,27486,27617,27839,27956,28104,28249,28399,28498,28594,28790,29044,29143,29327,29494,30184,30432,99856,100016,100218,100424,100621,115969,116133,123059,126113,126651,127069,127447,127624,127803,127986,128351,128548,139673"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\dec5fcd1d70b95b517bd22f6c9324073\\transformed\\fragment-1.5.4\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "253,267,299,2861,2866", "startColumns": "4,4,4,4,4", "startOffsets": "16556,17166,18809,167683,167853", "endLines": "253,267,299,2865,2869", "endColumns": "56,64,63,24,24", "endOffsets": "16608,17226,18868,167848,167997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\5104fe6d5a278875704c5617fdc0d9bb\\transformed\\activity-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "265,296", "startColumns": "4,4", "startOffsets": "17090,18645", "endColumns": "41,59", "endOffsets": "17127,18700"}}, {"source": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "54", "endLines": "6", "endColumns": "12", "endOffsets": "267"}, "to": {"startLines": "414", "startColumns": "4", "startOffsets": "30437", "endLines": "417", "endColumns": "12", "endOffsets": "30615"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\875a7da5ac63e51a49a89062b608b501\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "297", "startColumns": "4", "startOffsets": "18705", "endColumns": "53", "endOffsets": "18754"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\5bca0cfaae241dea23eba63d623ea919\\transformed\\appcompat-resources-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2170,2186,2192,3390,3406", "startColumns": "4,4,4,4,4", "startOffsets": "143718,144143,144321,182613,183024", "endLines": "2185,2191,2201,3405,3409", "endColumns": "24,24,24,24,24", "endOffsets": "144138,144316,144600,183019,183146"}}, {"source": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\android\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "54", "endOffsets": "66"}, "to": {"startLines": "335", "startColumns": "4", "startOffsets": "21316", "endColumns": "54", "endOffsets": "21366"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\956159063502be591b57ff4f5aaf7fef\\transformed\\drawee-3.6.0\\res\\values\\values.xml", "from": {"startLines": "2,136", "startColumns": "4,4", "startOffsets": "55,3906", "endLines": "135,218", "endColumns": "22,22", "endOffsets": "3901,5346"}, "to": {"startLines": "2870,3295", "startColumns": "4,4", "startOffsets": "168002,180856", "endLines": "3003,3377", "endColumns": "22,22", "endOffsets": "171848,182296"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\bfef782026e93cfc945e58eb2e9dfb5e\\transformed\\swiperefreshlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "24", "endOffsets": "287"}, "to": {"startLines": "3410", "startColumns": "4", "startOffsets": "183151", "endLines": "3413", "endColumns": "24", "endOffsets": "183317"}}, {"source": "D:\\copy from kpi tracker app\\expire management\\PharmExpireNative\\android\\app\\build\\generated\\res\\resValues\\debug\\values\\gradleResValues.xml", "from": {"startLines": "6,8", "startColumns": "4,4", "startOffsets": "159,265", "endColumns": "63,88", "endOffsets": "218,349"}, "to": {"startLines": "304,384", "startColumns": "4,4", "startOffsets": "19116,27297", "endColumns": "63,88", "endOffsets": "19175,27381"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\5f7cdf5cb9559c5edcdf8f836411df92\\transformed\\lifecycle-viewmodel-2.6.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "298", "startColumns": "4", "startOffsets": "18759", "endColumns": "49", "endOffsets": "18804"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\17365bb8d44a96e8cb68a585945c3ff1\\transformed\\lifecycle-runtime-2.6.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "295", "startColumns": "4", "startOffsets": "18602", "endColumns": "42", "endOffsets": "18640"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\ec4e31fa8d0cf74d3d6955f9c39b3c31\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "5,16,17,30,31,56,57,160,161,162,163,164,165,166,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,199,200,201,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,257,258,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,305,336,337,338,339,340,341,342,396,1797,1798,1802,1803,1807,2044,2045,2715,2749,2805,2840,3004,3037", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "254,973,1045,2093,2158,3703,3772,10758,10828,10896,10968,11038,11099,11173,12030,12091,12152,12214,12278,12340,12401,12469,12569,12629,12695,12768,12837,12894,12946,13461,13533,13609,13838,13897,13956,14016,14076,14136,14196,14256,14316,14376,14436,14496,14556,14615,14675,14735,14795,14855,14915,14975,15035,15095,15155,15215,15274,15334,15394,15453,15512,15571,15630,15689,16738,16773,17277,17332,17395,17450,17508,17564,17622,17683,17746,17803,17854,17912,17962,18023,18080,18146,18180,18215,19180,21371,21438,21510,21579,21648,21722,21794,28795,121852,121969,122170,122280,122481,139098,139170,161393,162966,165196,167002,171853,172535", "endLines": "5,16,17,30,31,56,57,160,161,162,163,164,165,166,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,199,200,201,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,257,258,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,305,336,337,338,339,340,341,342,396,1797,1801,1802,1806,1807,2044,2045,2720,2758,2839,2860,3036,3042", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "309,1040,1128,2153,2219,3767,3830,10823,10891,10963,11033,11094,11168,11241,12086,12147,12209,12273,12335,12396,12464,12564,12624,12690,12763,12832,12889,12941,13003,13528,13604,13669,13892,13951,14011,14071,14131,14191,14251,14311,14371,14431,14491,14551,14610,14670,14730,14790,14850,14910,14970,15030,15090,15150,15210,15269,15329,15389,15448,15507,15566,15625,15684,15743,16768,16803,17327,17390,17445,17503,17559,17617,17678,17741,17798,17849,17907,17957,18018,18075,18141,18175,18210,18245,19245,21433,21505,21574,21643,21717,21789,21877,28861,121964,122165,122275,122476,122605,139165,139232,161591,163262,166997,167678,172530,172697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\01b3cc6ba7b89a751b7226c9f68a2db0\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "294", "startColumns": "4", "startOffsets": "18559", "endColumns": "42", "endOffsets": "18597"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\a3cb7bf04a84099f6bc24510b14a8d32\\transformed\\autofill-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,19,20,27,32,37,44,53", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,934,994,1376,1656,1938,2322,2820", "endLines": "2,18,19,26,31,36,43,52,66", "endColumns": "67,12,59,12,12,12,12,12,24", "endOffsets": "118,929,989,1371,1651,1933,2317,2815,3867"}, "to": {"startLines": "159,1845,2010,2011,2018,2023,2028,2035,2697", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10690,125272,137212,137272,137654,137934,138216,138600,160672", "endLines": "159,1860,2010,2017,2022,2027,2034,2043,2710", "endColumns": "67,12,59,12,12,12,12,12,24", "endOffsets": "10753,126078,137267,137649,137929,138211,138595,139093,161253"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f8caf368debc3686ade571a4ec56cfe6\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "334", "startColumns": "4", "startOffsets": "21233", "endColumns": "82", "endOffsets": "21311"}}]}]}