<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":react-native-vector-icons" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-vector-icons\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":react-native-gesture-handler" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\copy from kpi tracker app\expire management\PharmExpireNative\node_modules\react-native-gesture-handler\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\main\assets"/><source path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\ReactNativeVectorIcons"><file name="fonts/AntDesign.ttf" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\ReactNativeVectorIcons\fonts\AntDesign.ttf"/><file name="fonts/Entypo.ttf" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Entypo.ttf"/><file name="fonts/EvilIcons.ttf" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\ReactNativeVectorIcons\fonts\EvilIcons.ttf"/><file name="fonts/Feather.ttf" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Feather.ttf"/><file name="fonts/FontAwesome.ttf" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome.ttf"/><file name="fonts/FontAwesome5_Brands.ttf" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome5_Brands.ttf"/><file name="fonts/FontAwesome5_Regular.ttf" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome5_Regular.ttf"/><file name="fonts/FontAwesome5_Solid.ttf" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome5_Solid.ttf"/><file name="fonts/FontAwesome6_Brands.ttf" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome6_Brands.ttf"/><file name="fonts/FontAwesome6_Regular.ttf" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome6_Regular.ttf"/><file name="fonts/FontAwesome6_Solid.ttf" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome6_Solid.ttf"/><file name="fonts/Fontisto.ttf" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Fontisto.ttf"/><file name="fonts/Foundation.ttf" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Foundation.ttf"/><file name="fonts/Ionicons.ttf" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Ionicons.ttf"/><file name="fonts/MaterialCommunityIcons.ttf" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\ReactNativeVectorIcons\fonts\MaterialCommunityIcons.ttf"/><file name="fonts/MaterialIcons.ttf" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\ReactNativeVectorIcons\fonts\MaterialIcons.ttf"/><file name="fonts/Octicons.ttf" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Octicons.ttf"/><file name="fonts/SimpleLineIcons.ttf" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\ReactNativeVectorIcons\fonts\SimpleLineIcons.ttf"/><file name="fonts/Zocial.ttf" path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Zocial.ttf"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\copy from kpi tracker app\expire management\PharmExpireNative\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>