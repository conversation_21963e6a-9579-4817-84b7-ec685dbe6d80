{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-7806a930e27e157686c6.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-ef44a739a84930e252c8.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [4]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-b40e34018d5c7b7a99e0.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [3]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-Debug-7b3feb529019be0099e5.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [1]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-82eae5abc1c13f91105a.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-Debug-99d04222f810173a8e83.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-e79a60550f6e350ab076.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-f5bf5a7a492657421299.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-ed84fcf540db50e36a55.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/arm64-v8a", "source": "D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}