import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { itemsAPI } from '../services/api';
import { formatDate, getExpirationStatus, getCategoryIcon } from '../utils/dateUtils';

const ItemListScreen = ({ navigation, route }) => {
  const [items, setItems] = useState([]);
  const [filteredItems, setFilteredItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [filter, setFilter] = useState(route?.params?.filter || 'all');

  useEffect(() => {
    fetchItems();
  }, [filter]);

  useEffect(() => {
    filterItems();
  }, [items, searchText]);

  const fetchItems = async () => {
    try {
      setLoading(true);
      let response;
      
      switch (filter) {
        case 'expired':
          response = await itemsAPI.getExpired();
          break;
        case 'expiring':
          response = await itemsAPI.getExpiringSoon();
          break;
        default:
          response = await itemsAPI.getAll();
      }
      
      setItems(response.data);
    } catch (error) {
      console.error('خطأ في جلب المنتجات:', error);
      Alert.alert('خطأ', 'فشل في تحميل المنتجات');
    } finally {
      setLoading(false);
    }
  };

  const filterItems = () => {
    let filtered = [...items];

    if (searchText) {
      filtered = filtered.filter(item =>
        item.item_name.toLowerCase().includes(searchText.toLowerCase()) ||
        item.category.toLowerCase().includes(searchText.toLowerCase()) ||
        (item.location && item.location.toLowerCase().includes(searchText.toLowerCase())) ||
        (item.notes && item.notes.toLowerCase().includes(searchText.toLowerCase()))
      );
    }

    setFilteredItems(filtered);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchItems();
    setRefreshing(false);
  };

  const handleDeleteItem = async (id) => {
    Alert.alert(
      'حذف المنتج',
      'هل أنت متأكد من حذف هذا المنتج؟',
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'حذف',
          style: 'destructive',
          onPress: async () => {
            try {
              await itemsAPI.delete(id);
              setItems(items.filter(item => item.id !== id));
              Alert.alert('نجح', 'تم حذف المنتج بنجاح');
            } catch (error) {
              console.error('خطأ في حذف المنتج:', error);
              Alert.alert('خطأ', 'فشل في حذف المنتج');
            }
          },
        },
      ]
    );
  };

  const renderItem = ({ item }) => {
    const expirationStatus = getExpirationStatus(item.expiration_date);
    
    return (
      <TouchableOpacity
        style={[styles.itemCard, { borderRightColor: expirationStatus.color }]}
        onPress={() => navigation.navigate('ItemDetail', { item })}
      >
        <View style={styles.itemHeader}>
          <View style={styles.itemActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('AddItem', { item })}
            >
              <Ionicons name="create" size={20} color="#6b7280" />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleDeleteItem(item.id)}
            >
              <Ionicons name="trash" size={20} color="#dc2626" />
            </TouchableOpacity>
          </View>
          <View style={styles.itemTitleContainer}>
            <View style={styles.itemInfo}>
              <Text style={styles.itemName}>{item.item_name}</Text>
              <Text style={styles.itemCategory}>{item.category}</Text>
            </View>
            <Text style={styles.categoryIcon}>{getCategoryIcon(item.category)}</Text>
          </View>
        </View>

        <View style={[styles.expirationBadge, { backgroundColor: expirationStatus.backgroundColor }]}>
          <Text style={[styles.expirationText, { color: expirationStatus.textColor }]}>
            {expirationStatus.message}
          </Text>
          <Text style={[styles.expirationDate, { color: expirationStatus.textColor }]}>
            تاريخ الانتهاء: {formatDate(item.expiration_date)}
          </Text>
        </View>

        {(item.quantity > 1 || item.location || item.notes) && (
          <View style={styles.itemDetails}>
            {item.quantity > 1 && (
              <Text style={styles.itemDetail}>الكمية: {item.quantity}</Text>
            )}
            {item.location && (
              <Text style={styles.itemDetail}>الموقع: {item.location}</Text>
            )}
            {item.notes && (
              <Text style={styles.itemDetail}>ملاحظات: {item.notes}</Text>
            )}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const getTitle = () => {
    switch (filter) {
      case 'expired':
        return 'المنتجات المنتهية';
      case 'expiring':
        return 'المنتجات التي تنتهي قريباً';
      default:
        return 'جميع المنتجات';
    }
  };

  return (
    <View style={styles.container}>
      {/* شريط البحث */}
      <View style={styles.searchContainer}>
        <TouchableOpacity onPress={() => setSearchText('')}>
          {searchText.length > 0 && <Ionicons name="close" size={20} color="#6b7280" />}
        </TouchableOpacity>
        <TextInput
          style={styles.searchInput}
          placeholder="البحث في المنتجات..."
          value={searchText}
          onChangeText={setSearchText}
          textAlign="right"
        />
        <Ionicons name="search" size={20} color="#6b7280" style={styles.searchIcon} />
      </View>

      {/* أزرار التصفية */}
      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'expired' && styles.filterButtonActive]}
          onPress={() => setFilter('expired')}
        >
          <Text style={[styles.filterButtonText, filter === 'expired' && styles.filterButtonTextActive]}>
            منتهية
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'expiring' && styles.filterButtonActive]}
          onPress={() => setFilter('expiring')}
        >
          <Text style={[styles.filterButtonText, filter === 'expiring' && styles.filterButtonTextActive]}>
            تنتهي قريباً
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'all' && styles.filterButtonActive]}
          onPress={() => setFilter('all')}
        >
          <Text style={[styles.filterButtonText, filter === 'all' && styles.filterButtonTextActive]}>
            الكل
          </Text>
        </TouchableOpacity>
      </View>

      {/* قائمة المنتجات */}
      <FlatList
        data={filteredItems}
        renderItem={renderItem}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContainer}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="cube-outline" size={64} color="#d1d5db" />
            <Text style={styles.emptyTitle}>لا توجد منتجات</Text>
            <Text style={styles.emptySubtitle}>
              {searchText ? 'جرب تعديل كلمات البحث' : 'أضف منتجك الأول للبدء'}
            </Text>
            <TouchableOpacity
              style={styles.addButton}
              onPress={() => navigation.navigate('AddItem')}
            >
              <Ionicons name="add" size={20} color="#fff" />
              <Text style={styles.addButtonText}>إضافة منتج</Text>
            </TouchableOpacity>
          </View>
        }
      />

      {/* زر الإضافة العائم */}
      {filteredItems.length > 0 && (
        <TouchableOpacity
          style={styles.fab}
          onPress={() => navigation.navigate('AddItem')}
        >
          <Ionicons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    margin: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  searchIcon: {
    marginLeft: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#1f2937',
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 16,
    gap: 8,
    justifyContent: 'flex-end',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f3f4f6',
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  filterButtonActive: {
    backgroundColor: '#2563eb',
    borderColor: '#2563eb',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: '#fff',
  },
  listContainer: {
    padding: 16,
  },
  itemCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderRightWidth: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  itemTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    fontSize: 24,
    marginLeft: 12,
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
    textAlign: 'right',
  },
  itemCategory: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'right',
  },
  itemActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 8,
  },
  expirationBadge: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  expirationText: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
    textAlign: 'right',
  },
  expirationDate: {
    fontSize: 12,
    opacity: 0.8,
    textAlign: 'right',
  },
  itemDetails: {
    gap: 4,
  },
  itemDetail: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'right',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 64,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 24,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2563eb',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  addButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#2563eb',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
});

export default ItemListScreen;
