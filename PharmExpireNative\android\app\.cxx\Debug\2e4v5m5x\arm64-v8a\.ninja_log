# ninja log v5
4849	12282	7742461417937706	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/ComponentDescriptors.cpp.o	ccbb8f641031a7c2
2	61	0	D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/arm64-v8a/CMakeFiles/cmake.verify_globs	c7cab3c21e4bfd22
0	26	0	clean	d4f0c3606ddd5f71
3267	11136	7742461406315007	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	93fed645a5f5a5ce
5798	14069	7742461435620667	CMakeFiles/appmodules.dir/3d65668b9a8facb0ef0b395fec5f0274/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	2668d8827efc27a
332	4609	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	d4a87f71e60aea77
1129	5927	0	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	5c07cbf95fa71cc1
3821	11163	7742461406653766	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	c87cbdee19c5eec3
3	9555	7742461390340636	CMakeFiles/appmodules.dir/OnLoad.cpp.o	e88965732e9357ce
2245	8782	7742461382729460	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	daef7113f7eb40b
882	5873	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	d78537b5fbc6c494
654	6048	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o	e5ec58d1e3933332
2885	11019	7742461405142917	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	fbef05e36d376b21
5301	11290	7742461407968814	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/EventEmitters.cpp.o	12bd74afe35baf79
1257	11355	7742461408366984	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	3823a1094f509887
6264	11526	7742461410380947	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	6e3985b4e6e364ef
33	7315	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	d94597c8a6e4b227
6625	12869	7742461423726972	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/ShadowNodes.cpp.o	1dc2f783cbb2715e
7316	9647	7742466999548707	D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/build/intermediates/cxx/Debug/2e4v5m5x/obj/arm64-v8a/libappmodules.so	931c32b23bc454f5
4	146	0	D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/arm64-v8a/CMakeFiles/cmake.verify_globs	c7cab3c21e4bfd22
