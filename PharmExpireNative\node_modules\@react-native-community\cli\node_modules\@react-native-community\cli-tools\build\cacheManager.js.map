{"version": 3, "names": ["loadCache", "name", "cacheRaw", "fs", "readFileSync", "path", "resolve", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cache", "JSON", "parse", "e", "code", "saveCache", "logger", "debug", "undefined", "fullPath", "mkdirSync", "dirname", "recursive", "writeFileSync", "stringify", "legacyPath", "os", "homedir", "cachePath", "appDirs", "appName", "existsSync", "removeProjectCache", "cacheRootPath", "rmSync", "error", "chalk", "underline", "join", "get", "key", "set", "value"], "sources": ["../src/cacheManager.ts"], "sourcesContent": ["import path from 'path';\nimport fs from 'fs';\nimport os from 'os';\nimport appDirs from 'appdirsjs';\nimport chalk from 'chalk';\nimport logger from './logger';\n\ntype CacheKey =\n  | 'eTag'\n  | 'lastChecked'\n  | 'latestVersion'\n  | 'dependencies'\n  | 'podfile'\n  | 'podfileLock'\n  | 'lastUsedIOSDeviceId';\ntype Cache = {[key in CacheKey]?: string};\n\nfunction loadCache(name: string): Cache | undefined {\n  try {\n    const cacheRaw = fs.readFileSync(\n      path.resolve(getCacheRootPath(), name),\n      'utf8',\n    );\n    const cache = JSON.parse(cacheRaw);\n    return cache;\n  } catch (e) {\n    if ((e as any).code === 'ENOENT') {\n      // Create cache file since it doesn't exist.\n      saveCache(name, {});\n    }\n    logger.debug('No cache found');\n    return undefined;\n  }\n}\n\nfunction saveCache(name: string, cache: Cache) {\n  const fullPath = path.resolve(getCacheRootPath(), name);\n\n  fs.mkdirSync(path.dirname(fullPath), {recursive: true});\n  fs.writeFileSync(fullPath, JSON.stringify(cache, null, 2));\n}\n\n/**\n * Returns the path string of `$HOME/.react-native-cli`.\n *\n * In case it doesn't exist, it will be created.\n */\nfunction getCacheRootPath() {\n  const legacyPath = path.resolve(os.homedir(), '.react-native-cli', 'cache');\n  const cachePath = appDirs({appName: 'react-native-cli', legacyPath}).cache;\n\n  if (!fs.existsSync(cachePath)) {\n    fs.mkdirSync(cachePath, {recursive: true});\n  }\n\n  return cachePath;\n}\n\nfunction removeProjectCache(name: string) {\n  const cacheRootPath = getCacheRootPath();\n  try {\n    const fullPath = path.resolve(cacheRootPath, name);\n\n    if (fs.existsSync(fullPath)) {\n      fs.rmSync(fullPath, {recursive: true});\n    }\n  } catch {\n    logger.error(\n      `Failed to remove cache for ${name}. If you experience any issues when running freshly initialized project, please remove the \"${chalk.underline(\n        path.join(cacheRootPath, name),\n      )}\" folder manually.`,\n    );\n  }\n}\n\nfunction get(name: string, key: CacheKey): string | undefined {\n  const cache = loadCache(name);\n  if (cache) {\n    return cache[key];\n  }\n  return undefined;\n}\n\nfunction set(name: string, key: CacheKey, value: string) {\n  const cache = loadCache(name);\n  if (cache) {\n    cache[key] = value;\n    saveCache(name, cache);\n  }\n}\n\nexport default {\n  get,\n  set,\n  removeProjectCache,\n  getCacheRootPath,\n};\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAA8B;AAY9B,SAASA,SAAS,CAACC,IAAY,EAAqB;EAClD,IAAI;IACF,MAAMC,QAAQ,GAAGC,aAAE,CAACC,YAAY,CAC9BC,eAAI,CAACC,OAAO,CAACC,gBAAgB,EAAE,EAAEN,IAAI,CAAC,EACtC,MAAM,CACP;IACD,MAAMO,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACR,QAAQ,CAAC;IAClC,OAAOM,KAAK;EACd,CAAC,CAAC,OAAOG,CAAC,EAAE;IACV,IAAKA,CAAC,CAASC,IAAI,KAAK,QAAQ,EAAE;MAChC;MACAC,SAAS,CAACZ,IAAI,EAAE,CAAC,CAAC,CAAC;IACrB;IACAa,eAAM,CAACC,KAAK,CAAC,gBAAgB,CAAC;IAC9B,OAAOC,SAAS;EAClB;AACF;AAEA,SAASH,SAAS,CAACZ,IAAY,EAAEO,KAAY,EAAE;EAC7C,MAAMS,QAAQ,GAAGZ,eAAI,CAACC,OAAO,CAACC,gBAAgB,EAAE,EAAEN,IAAI,CAAC;EAEvDE,aAAE,CAACe,SAAS,CAACb,eAAI,CAACc,OAAO,CAACF,QAAQ,CAAC,EAAE;IAACG,SAAS,EAAE;EAAI,CAAC,CAAC;EACvDjB,aAAE,CAACkB,aAAa,CAACJ,QAAQ,EAAER,IAAI,CAACa,SAAS,CAACd,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAC5D;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASD,gBAAgB,GAAG;EAC1B,MAAMgB,UAAU,GAAGlB,eAAI,CAACC,OAAO,CAACkB,aAAE,CAACC,OAAO,EAAE,EAAE,mBAAmB,EAAE,OAAO,CAAC;EAC3E,MAAMC,SAAS,GAAG,IAAAC,oBAAO,EAAC;IAACC,OAAO,EAAE,kBAAkB;IAAEL;EAAU,CAAC,CAAC,CAACf,KAAK;EAE1E,IAAI,CAACL,aAAE,CAAC0B,UAAU,CAACH,SAAS,CAAC,EAAE;IAC7BvB,aAAE,CAACe,SAAS,CAACQ,SAAS,EAAE;MAACN,SAAS,EAAE;IAAI,CAAC,CAAC;EAC5C;EAEA,OAAOM,SAAS;AAClB;AAEA,SAASI,kBAAkB,CAAC7B,IAAY,EAAE;EACxC,MAAM8B,aAAa,GAAGxB,gBAAgB,EAAE;EACxC,IAAI;IACF,MAAMU,QAAQ,GAAGZ,eAAI,CAACC,OAAO,CAACyB,aAAa,EAAE9B,IAAI,CAAC;IAElD,IAAIE,aAAE,CAAC0B,UAAU,CAACZ,QAAQ,CAAC,EAAE;MAC3Bd,aAAE,CAAC6B,MAAM,CAACf,QAAQ,EAAE;QAACG,SAAS,EAAE;MAAI,CAAC,CAAC;IACxC;EACF,CAAC,CAAC,MAAM;IACNN,eAAM,CAACmB,KAAK,CACT,8BAA6BhC,IAAK,+FAA8FiC,gBAAK,CAACC,SAAS,CAC9I9B,eAAI,CAAC+B,IAAI,CAACL,aAAa,EAAE9B,IAAI,CAAC,CAC9B,oBAAmB,CACtB;EACH;AACF;AAEA,SAASoC,GAAG,CAACpC,IAAY,EAAEqC,GAAa,EAAsB;EAC5D,MAAM9B,KAAK,GAAGR,SAAS,CAACC,IAAI,CAAC;EAC7B,IAAIO,KAAK,EAAE;IACT,OAAOA,KAAK,CAAC8B,GAAG,CAAC;EACnB;EACA,OAAOtB,SAAS;AAClB;AAEA,SAASuB,GAAG,CAACtC,IAAY,EAAEqC,GAAa,EAAEE,KAAa,EAAE;EACvD,MAAMhC,KAAK,GAAGR,SAAS,CAACC,IAAI,CAAC;EAC7B,IAAIO,KAAK,EAAE;IACTA,KAAK,CAAC8B,GAAG,CAAC,GAAGE,KAAK;IAClB3B,SAAS,CAACZ,IAAI,EAAEO,KAAK,CAAC;EACxB;AACF;AAAC,eAEc;EACb6B,GAAG;EACHE,GAAG;EACHT,kBAAkB;EAClBvB;AACF,CAAC;AAAA"}