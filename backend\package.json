{"name": "pharmacy-expire-backend", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["pharmacy", "expiration", "management"], "author": "", "license": "ISC", "description": "Backend for Pharmacy Expire Management App", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.21.2", "sqlite3": "^5.1.7"}, "devDependencies": {"nodemon": "^3.1.10"}}