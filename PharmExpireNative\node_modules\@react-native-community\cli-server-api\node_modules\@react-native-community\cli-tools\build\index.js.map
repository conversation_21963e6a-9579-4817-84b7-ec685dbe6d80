{"version": 3, "names": [], "sources": ["../src/index.ts"], "sourcesContent": ["export {default as logger} from './logger';\nexport {default as isPackagerRunning} from './isPackagerRunning';\nexport {default as getDefaultUserTerminal} from './getDefaultUserTerminal';\nexport {fetch, fetchToTemp} from './fetch';\nexport {default as launchEditor} from './launchEditor';\nexport * as version from './releaseChecker';\nexport {default as resolveNodeModuleDir} from './resolveNodeModuleDir';\nexport {getLoader, NoopLoader, Loader} from './loader';\nexport {default as findProjectRoot} from './findProjectRoot';\nexport {default as printRunDoctorTip} from './printRunDoctorTip';\nexport * from './prompt';\nexport * as link from './doclink';\nexport {default as startServerInNewWindow} from './startServerInNewWindow';\nexport {default as findDevServerPort} from './findDevServerPort';\nexport * from './port';\nexport {default as cacheManager} from './cacheManager';\nexport {default as runSudo} from './runSudo';\nexport {default as unixifyPaths} from './unixifyPaths';\n\nexport * from './errors';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AAAuD;AAAA;AAEvD;AACA;AACA;AACA;AACA;AAAA;EAAA;EAAA;EAAA;EAAA;IAAA;IAAA;MAAA;IAAA;EAAA;AAAA;AAAyB;AAAA;AAEzB;AACA;AACA;AAAA;EAAA;EAAA;EAAA;EAAA;IAAA;IAAA;MAAA;IAAA;EAAA;AAAA;AACA;AACA;AACA;AAEA;AAAA;EAAA;EAAA;EAAA;EAAA;IAAA;IAAA;MAAA;IAAA;EAAA;AAAA;AAAyB;AAAA;AAAA"}