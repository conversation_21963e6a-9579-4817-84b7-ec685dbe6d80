import { itemsAPI } from '../frontend/src/services/api.js';

// Sample data for testing
const sampleItems = [
  {
    item_name: "Aspirin 100mg",
    category: "Medicine",
    expiration_date: "2024-08-15",
    notes: "Pain relief medication",
    quantity: 2,
    location: "Medicine Cabinet A"
  },
  {
    item_name: "Vitamin D3",
    category: "Wellness",
    expiration_date: "2025-03-20",
    notes: "Daily supplement",
    quantity: 1,
    location: "Vitamin Shelf"
  },
  {
    item_name: "Baby Formula",
    category: "Mom & Baby",
    expiration_date: "2024-07-20",
    notes: "Organic formula powder",
    quantity: 3,
    location: "Baby Section"
  },
  {
    item_name: "Face Cream",
    category: "Cosmetic",
    expiration_date: "2024-12-01",
    notes: "Anti-aging moisturizer",
    quantity: 1,
    location: "Cosmetics Shelf"
  },
  {
    item_name: "Protein Powder",
    category: "Wellness",
    expiration_date: "2024-07-25",
    notes: "Chocolate flavor",
    quantity: 1,
    location: "Supplements"
  },
  {
    item_name: "Cough Syrup",
    category: "Medicine",
    expiration_date: "2023-12-15",
    notes: "Cherry flavored - EXPIRED",
    quantity: 1,
    location: "Medicine Cabinet B"
  }
];

console.log('Sample data created for testing:', sampleItems);
