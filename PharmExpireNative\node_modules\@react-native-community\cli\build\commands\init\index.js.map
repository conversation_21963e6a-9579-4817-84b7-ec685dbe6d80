{"version": 3, "names": ["func", "init", "detached", "name", "description", "options", "parse", "val", "Object", "fromEntries", "split", "map", "option", "key", "value"], "sources": ["../../../src/commands/init/index.ts"], "sourcesContent": ["import init from './init';\n\nexport default {\n  func: init,\n  detached: true,\n  name: 'init [projectName]',\n  description:\n    'New app will be initialized in the directory of the same name. Android and iOS projects will use this name for publishing setup.',\n  options: [\n    {\n      name: '--version <string>',\n      description: 'React Native version to install in the template',\n    },\n    {\n      name: '--template <string>',\n      description:\n        'Uses a custom template. Valid arguments are the ones supported by `npm install [package]` or `yarn add [package]`, if you are using `--pm yarn` option',\n    },\n    {\n      name: '--pm <string>',\n      description:\n        'Use specific package manager to initialize the project. Available options: `yarn`, `npm`, `bun`. Default: `npm`',\n    },\n    {\n      name: '--directory <string>',\n      description: 'Uses a custom directory instead of `<projectName>`.',\n    },\n    {\n      name: '--title <string>',\n      description: 'Uses a custom app title name for application',\n    },\n    {\n      name: '--skip-install',\n      description: 'Skips dependencies installation step',\n    },\n    {\n      name: '--install-pods [boolean]',\n      description:\n        'Determine if CocoaPods should be installed when initializing a project',\n    },\n    {\n      name: '--package-name <string>',\n      description:\n        'Inits a project with a custom package name (Android) and bundle ID (iOS), e.g. com.example.app',\n    },\n    {\n      name: '--platform-name <string>',\n      description:\n        'Name of out of tree platform to be used for ex. react-native-macos. This flag is optional as it should be passed automatically by out of tree platform. It needs to match the name of the platform declared in package.json',\n    },\n    {\n      name: '--skip-git-init',\n      description: 'Skip git repository initialization',\n    },\n    {\n      name: '--replace-directory [boolean]',\n      description: 'Replaces the directory if it already exists.',\n    },\n    {\n      name: '--yarn-config-options <string>',\n      description:\n        'Passes extra options that will be added to `.yarnrc.yml` file, format: key=value,key2=value2.',\n      parse: (val: string): Record<string, string> => {\n        return Object.fromEntries(\n          val.split(',').map((option) => {\n            const [key, value] = option.split('=');\n            return [key, value];\n          }),\n        );\n      },\n    },\n  ],\n};\n"], "mappings": ";;;;;;AAAA;AAA0B;AAAA,eAEX;EACbA,IAAI,EAAEC,aAAI;EACVC,QAAQ,EAAE,IAAI;EACdC,IAAI,EAAE,oBAAoB;EAC1BC,WAAW,EACT,kIAAkI;EACpIC,OAAO,EAAE,CACP;IACEF,IAAI,EAAE,oBAAoB;IAC1BC,WAAW,EAAE;EACf,CAAC,EACD;IACED,IAAI,EAAE,qBAAqB;IAC3BC,WAAW,EACT;EACJ,CAAC,EACD;IACED,IAAI,EAAE,eAAe;IACrBC,WAAW,EACT;EACJ,CAAC,EACD;IACED,IAAI,EAAE,sBAAsB;IAC5BC,WAAW,EAAE;EACf,CAAC,EACD;IACED,IAAI,EAAE,kBAAkB;IACxBC,WAAW,EAAE;EACf,CAAC,EACD;IACED,IAAI,EAAE,gBAAgB;IACtBC,WAAW,EAAE;EACf,CAAC,EACD;IACED,IAAI,EAAE,0BAA0B;IAChCC,WAAW,EACT;EACJ,CAAC,EACD;IACED,IAAI,EAAE,yBAAyB;IAC/BC,WAAW,EACT;EACJ,CAAC,EACD;IACED,IAAI,EAAE,0BAA0B;IAChCC,WAAW,EACT;EACJ,CAAC,EACD;IACED,IAAI,EAAE,iBAAiB;IACvBC,WAAW,EAAE;EACf,CAAC,EACD;IACED,IAAI,EAAE,+BAA+B;IACrCC,WAAW,EAAE;EACf,CAAC,EACD;IACED,IAAI,EAAE,gCAAgC;IACtCC,WAAW,EACT,+FAA+F;IACjGE,KAAK,EAAGC,GAAW,IAA6B;MAC9C,OAAOC,MAAM,CAACC,WAAW,CACvBF,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,MAAM,IAAK;QAC7B,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,GAAGF,MAAM,CAACF,KAAK,CAAC,GAAG,CAAC;QACtC,OAAO,CAACG,GAAG,EAAEC,KAAK,CAAC;MACrB,CAAC,CAAC,CACH;IACH;EACF,CAAC;AAEL,CAAC;AAAA"}