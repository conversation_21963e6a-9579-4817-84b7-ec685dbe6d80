{"version": 3, "names": ["reactLogoArray", "getWelcomeMessage", "reactNativeVersion", "learnOnceMessage", "banner", "chalk", "cyan", "join", "cyan<PERSON><PERSON>", "bold", "dim"], "sources": ["../../../src/commands/init/banner.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nconst reactLogoArray = [\n  '                                                          ',\n  '               ######                ######               ',\n  '             ###     ####        ####     ###             ',\n  '            ##          ###    ###          ##            ',\n  '            ##             ####             ##            ',\n  '            ##             ####             ##            ',\n  '            ##           ##    ##           ##            ',\n  '            ##         ###      ###         ##            ',\n  '             ##  ########################  ##             ',\n  '          ######    ###            ###    ######          ',\n  '      ###     ##    ##              ##    ##     ###      ',\n  '   ###         ## ###      ####      ### ##         ###   ',\n  '  ##           ####      ########      ####           ##  ',\n  ' ##             ###     ##########     ###             ## ',\n  '  ##           ####      ########      ####           ##  ',\n  '   ###         ## ###      ####      ### ##         ###   ',\n  '      ###     ##    ##              ##    ##     ###      ',\n  '          ######    ###            ###    ######          ',\n  '             ##  ########################  ##             ',\n  '            ##         ###      ###         ##            ',\n  '            ##           ##    ##           ##            ',\n  '            ##             ####             ##            ',\n  '            ##             ####             ##            ',\n  '            ##          ###    ###          ##            ',\n  '             ###     ####        ####     ###             ',\n  '               ######                ######               ',\n  '                                                          ',\n];\n\nconst getWelcomeMessage = (reactNativeVersion: string = '') => {\n  if (reactNativeVersion) {\n    return `              Welcome to React Native ${reactNativeVersion}!                `;\n  }\n  return '                  Welcome to React Native!                ';\n};\nconst learnOnceMessage =\n  '                 Learn once, write anywhere               ';\n\nexport default function banner(reactNativeVersion?: string) {\n  return `${chalk.cyan(reactLogoArray.join('\\n'))}\n\n${chalk.cyanBright.bold(getWelcomeMessage(reactNativeVersion))}\n${chalk.dim(learnOnceMessage)}\n`;\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0B;AAE1B,MAAMA,cAAc,GAAG,CACrB,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,CAC7D;AAED,MAAMC,iBAAiB,GAAG,CAACC,kBAA0B,GAAG,EAAE,KAAK;EAC7D,IAAIA,kBAAkB,EAAE;IACtB,OAAQ,yCAAwCA,kBAAmB,mBAAkB;EACvF;EACA,OAAO,4DAA4D;AACrE,CAAC;AACD,MAAMC,gBAAgB,GACpB,4DAA4D;AAE/C,SAASC,MAAM,CAACF,kBAA2B,EAAE;EAC1D,OAAQ,GAAEG,gBAAK,CAACC,IAAI,CAACN,cAAc,CAACO,IAAI,CAAC,IAAI,CAAC,CAAE;AAClD;AACA,EAAEF,gBAAK,CAACG,UAAU,CAACC,IAAI,CAACR,iBAAiB,CAACC,kBAAkB,CAAC,CAAE;AAC/D,EAAEG,gBAAK,CAACK,GAAG,CAACP,gBAAgB,CAAE;AAC9B,CAAC;AACD"}