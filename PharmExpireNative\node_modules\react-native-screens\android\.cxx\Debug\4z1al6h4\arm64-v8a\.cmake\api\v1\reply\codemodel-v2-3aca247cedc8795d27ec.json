{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "rnscreens", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "rnscreens::@6890427a1f51a3e7e1df", "jsonFile": "target-rnscreens-Debug-cb9eadf1b506e1786fdb.json", "name": "rnscreens", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native-screens/android/.cxx/Debug/4z1al6h4/arm64-v8a", "source": "D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native-screens/android"}, "version": {"major": 2, "minor": 3}}