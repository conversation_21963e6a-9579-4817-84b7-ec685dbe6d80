CMake Warning in D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt:
  The object file directory

    D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/./

  has 199 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


CMake Warning in D:/copy from kpi tracker app/expire management/PharmExpireNative/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt:
  The object file directory

    D:/copy from kpi tracker app/expire management/PharmExpireNative/android/app/.cxx/Debug/2e4v5m5x/x86/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/./

  has 185 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


