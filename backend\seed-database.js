import { Item } from './models/Item.js';
import { initializeDatabase } from './database/db.js';

const sampleItems = [
  {
    item_name: "Aspirin 100mg",
    category: "Medicine",
    expiration_date: "2024-08-15",
    notes: "Pain relief medication",
    quantity: 2,
    location: "Medicine Cabinet A"
  },
  {
    item_name: "Vitamin D3",
    category: "Wellness",
    expiration_date: "2025-03-20",
    notes: "Daily supplement",
    quantity: 1,
    location: "Vitamin Shelf"
  },
  {
    item_name: "Baby Formula",
    category: "Mom & Baby",
    expiration_date: "2024-07-20",
    notes: "Organic formula powder",
    quantity: 3,
    location: "Baby Section"
  },
  {
    item_name: "Face Cream",
    category: "Cosmetic",
    expiration_date: "2024-12-01",
    notes: "Anti-aging moisturizer",
    quantity: 1,
    location: "Cosmetics Shelf"
  },
  {
    item_name: "Protein Powder",
    category: "Wellness",
    expiration_date: "2024-07-25",
    notes: "Chocolate flavor",
    quantity: 1,
    location: "Supplements"
  },
  {
    item_name: "Cough Syrup",
    category: "Medicine",
    expiration_date: "2023-12-15",
    notes: "Cherry flavored - EXPIRED",
    quantity: 1,
    location: "Medicine Cabinet B"
  },
  {
    item_name: "Organic Honey",
    category: "Food",
    expiration_date: "2026-01-15",
    notes: "Raw unprocessed honey",
    quantity: 1,
    location: "Pantry Shelf 2"
  },
  {
    item_name: "Baby Wipes",
    category: "Mom & Baby",
    expiration_date: "2024-07-18",
    notes: "Sensitive skin formula",
    quantity: 5,
    location: "Baby Section"
  },
  {
    item_name: "Sunscreen SPF 50",
    category: "Cosmetic",
    expiration_date: "2024-07-30",
    notes: "Water resistant",
    quantity: 2,
    location: "Summer Items"
  },
  {
    item_name: "Multivitamin",
    category: "Wellness",
    expiration_date: "2025-01-10",
    notes: "Daily multivitamin for adults",
    quantity: 1,
    location: "Vitamin Shelf"
  }
];

const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...');
    
    // Initialize database
    await initializeDatabase();
    
    // Add sample items
    for (const itemData of sampleItems) {
      try {
        const item = await Item.create(itemData);
        console.log(`✅ Added: ${item.item_name}`);
      } catch (error) {
        console.log(`❌ Failed to add ${itemData.item_name}:`, error.message);
      }
    }
    
    console.log('🎉 Database seeding completed!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    process.exit(1);
  }
};

seedDatabase();
